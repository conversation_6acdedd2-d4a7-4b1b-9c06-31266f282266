package com.facishare.open.outer.oa.connector.common.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * CRM部门字段枚举
 */
@Getter
@AllArgsConstructor
public enum CRMDepartmentFieldEnum implements Serializable {
    NAME("name", "部门名称"), DEPT_ID("dept_id", "部门ID"), PARENT_DEPT_ID("parent_dept_id", "上级部门ID"), ORDER("order", "排序"),
    STATUS("status", "部门状态"), LEADER_ID("leader_id", "部门负责人ID"),;

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static CRMDepartmentFieldEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CRMDepartmentFieldEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}