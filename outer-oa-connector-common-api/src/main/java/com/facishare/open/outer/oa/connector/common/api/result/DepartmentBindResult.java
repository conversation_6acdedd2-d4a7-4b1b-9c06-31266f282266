package com.facishare.open.outer.oa.connector.common.api.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 部门绑定信息结果类
 */
@Data
public class DepartmentBindResult implements Serializable {


    private String id;

    /**
     * CRM部门名称
     */
    private String crmDeptName;

    /**
     * CRM部门ID
     */
    private String crmDeptId;

    /**
     * CRM部门状态（启用/停用）
     */
    private Boolean crmDeptStatus;

    /**
     * 外部系统部门名称
     */
    private String outDeptName;

    /**
     * 外部系统部门ID
     */
    private String outDeptId;

    /**
     * 绑定状态
     */
    private Boolean bindStatus;
}