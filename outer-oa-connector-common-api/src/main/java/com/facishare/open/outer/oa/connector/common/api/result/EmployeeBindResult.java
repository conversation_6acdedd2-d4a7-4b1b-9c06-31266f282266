package com.facishare.open.outer.oa.connector.common.api.result;

import lombok.Data;

import java.io.Serializable;

@Data
public class EmployeeBindResult  implements Serializable {
    /**
     * 映射表的id
     */

    private String id;

    private String CrmEmpName;

    private String crmPhone;

    private String crmMainDept;
    /**
     * crm动态查询字段，匹配 LIST_OBJECT_LAYOUT_MAPPING
     */
    private String crmQueryField;

    private String crmEmpStatus;

    private String outerField;

    private String outEmpDept;


    private Boolean bindStatus;

}
