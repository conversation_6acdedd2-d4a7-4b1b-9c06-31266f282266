package com.facishare.open.outer.oa.connector.common.api.admin;

import com.facishare.open.outer.oa.connector.common.api.enums.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用于实现连接器基础配置的保存和获取
 */

@Data
public class BaseConnectorVo implements Serializable {

     /**
      * 连接器中心id
      */

     private String dataCenterId;
     /**
      * 连接器名字 飞书连接器
      */
     private String connectorName;

     /**
      * 数据中心名字
      */
     private String dataCenterName;
     /**
      * 渠道
      */
     private ChannelEnum channel;

     /**
      * 应用类型
      * 
      * @see OuterOaAppInfoTypeEnum
      */
     private OuterOaAppInfoTypeEnum appType;

     /**
      * 认证类型
      * 
      * @see AuthTypeEnum
      */
     private String authType;

     private BindStatusEnum bindStatus;

     /**
      * 推送配置 true:开启 false 关闭
      */
     private boolean alertConfig;


     /**
      * 推送消息提醒 包含：CRM待办、CRM提醒、CRM日程、CRM审批等类型
      * 
      * @see com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum
      */
     private List<AlertTypeEnum> alertTypes;

}
