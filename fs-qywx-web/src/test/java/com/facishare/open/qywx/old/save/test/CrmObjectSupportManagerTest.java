package com.facishare.open.qywx.old.save.test;

import com.facishare.open.qywx.BaseTest;
import com.facishare.open.qywx.web.constant.CreateObjectEnum;
import com.facishare.open.qywx.web.manager.CrmObjectSupportManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CrmObjectSupportManagerTest extends BaseTest {
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;

    @Test
    public void createDefineObject() {
        boolean defineObject = crmObjectSupportManager.createDefineObject(74164, CreateObjectEnum.WechatConversionObj.name());
        System.out.println(defineObject);
    }
}
