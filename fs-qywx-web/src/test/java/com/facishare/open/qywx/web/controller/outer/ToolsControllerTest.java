package com.facishare.open.qywx.web.controller.outer;

import com.alibaba.fastjson.JSON;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.web.arg.*;
import com.facishare.open.qywx.web.controller.inner.ControllerInnerRestProxy;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ToolsControllerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ToolsController toolsController;

    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;

    @Test
    public void outAccountToFsAccountBatchTest() throws Exception {
        SelectSqlArg arg = new SelectSqlArg();
        arg.setSql("UPDATE \"public\".\"outer_oa_enterprise_bind\" SET \"channel\" = 'qywx', \"fs_ea\" = 'lgg6737', \"out_ea\" = 'wpwx1mDAAAqZ8RdkvINaBLjwm1AzhNwA', \"app_id\" = 'dkdf3684b6720635f7', \"connect_info\" = '{\"agentId\":1000002,\"alertConfig\":false,\"appType\":\"serviceRepDev\",\"channel\":\"qywx\",\"connectorName\":\"企业微信\",\"dataCenterId\":\"67f613299c90100001b78ac7\",\"dataCenterName\":\"企业微信\",\"isFirstLand\":true,\"isRetainInformation\":false,\"qywxCorpId\":\"wpwx1mDAAAqZ8RdkvINaBLjwm1AzhNwA\",\"qywxDepartmentId\":\"1\",\"qywxEnterpriseName\":\"没座哦\"}', \"bind_status\" = 'normal', \"create_time\" = *************, \"update_time\" = *************, \"bind_type\" = 'manual' WHERE \"id\" = '67f613299c90100001b78ac7';");
        arg.setToken("erpdss_qywx_1744966717893");
        Result<Integer> mapResult = toolsController.updateSql(arg);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }
    @Test
    public void detail() {
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsDepartmentServiceProxy.detail(84883,"1002");
        System.out.println(result);
    }
}
