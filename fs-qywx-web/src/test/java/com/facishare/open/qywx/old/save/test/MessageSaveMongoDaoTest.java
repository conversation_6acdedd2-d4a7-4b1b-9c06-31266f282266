package com.facishare.open.qywx.old.save.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.BaseTest;
import com.facishare.open.qywx.web.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.web.mongo.dao.MessageSaveMongoDao;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

@Slf4j
public class MessageSaveMongoDaoTest extends BaseTest {

    @Autowired
    private MessageSaveMongoDao messageSaveMongoDao;
    @Test
    public void testMessage(){
        List<MessageSaveDoc> docList = new LinkedList<>();
        for (int i = 0; i < 10; i++) {
            MessageSaveDoc doc = new MessageSaveDoc();
            doc.setId(ObjectId.get());
            doc.setEi(74860);
            doc.setFsEa("74860");
            doc.setContent("hello mongo -- " + i);
            doc.setFromUser("chenzongxin");
            doc.setToList(Lists.newArrayList("wmQZ1uJQAAP76E4294nLs3QgHvThowFA"));
            doc.setRoomId("test");
            doc.setMessageId("message_id_1000");
            doc.setMessageTime(System.currentTimeMillis() + i);
            doc.setMessageType("text");
            doc.setSeq(852L + i);
            doc.setCreateTime(System.currentTimeMillis());
            doc.setUpdateTime(System.currentTimeMillis());
            docList.add(doc);
        }



        BulkWriteResult result = messageSaveMongoDao.batchReplace(74860, docList);
        System.out.println(result);
        //MessageSaveDoc messageSaveDoc = messageSaveMongoDao.getById(84883, doc.getId().toString());
        //System.out.println(messageSaveDoc);
    }

    @Test
    public void testQueryPreMessageTime() {
        QueryMessageArg queryMessageArg=new QueryMessageArg();
        queryMessageArg.setFsEa("84883");
        queryMessageArg.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        queryMessageArg.setPageNum(1);
        queryMessageArg.setPageSize(20);
        queryMessageArg.setReceiveIds("17610501076");
        queryMessageArg.setSenderIds("13681318718");
        queryMessageArg.setLabelValue(3);
        queryMessageArg.setRoomId("wrQZ1uJQAA_uekrF71YiVvFnDofzXaSg");
        queryMessageArg.setLimit(3);
        List<MessageSaveDoc> docs = messageSaveMongoDao.queryPreMessageTime(84883, queryMessageArg);
        System.out.println(docs);
    }

    @Test
    public void testPageByFilters() {
        QueryMessageArg queryMessageArg=new QueryMessageArg();
        queryMessageArg.setFsEa("84883");
        queryMessageArg.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        queryMessageArg.setPageNum(20);
        queryMessageArg.setPageSize(2);
        queryMessageArg.setReceiveIds("chenzongxin");
        queryMessageArg.setSenderIds("13681318718");
        queryMessageArg.setLabelValue(3);
        queryMessageArg.setRoomId("wrQZ1uJQAA_uekrF71YiVvFnDofzXaSg");
        queryMessageArg.setLimit(3);
        queryMessageArg.setLastMessageTime(1672665893766L);
        queryMessageArg.setPreMessageTime(1669987493000L);
        List<MessageSaveDoc> docs = messageSaveMongoDao.pageByFilters(84883, queryMessageArg);
        System.out.println(docs);
    }

    @Test
    public void testQueryRoomIds() {
        QueryMessageArg queryMessageArg=new QueryMessageArg();
        queryMessageArg.setFsEa("84883");
        queryMessageArg.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        queryMessageArg.setPageNum(20);
        queryMessageArg.setPageSize(1);
        queryMessageArg.setReceiveIds("chenzongxin");
        queryMessageArg.setSenderIds("wmQZ1uJQAAP76E4294nLs3QgHvThowFA");
        queryMessageArg.setLabelValue(3);
        //queryMessageArg.setRoomId("wrQZ1uJQAA_uekrF71YiVvFnDofzXaSg");
        queryMessageArg.setLimit(3);
        queryMessageArg.setLastMessageTime(System.currentTimeMillis());
        queryMessageArg.setPreMessageTime(1672658268000L);
        FindIterable<MessageSaveDoc> docs = messageSaveMongoDao.queryRoomIds(84883, queryMessageArg);
        for(MessageSaveDoc doc : docs) {
            System.out.println(doc);
        }
        System.out.println(docs);
    }

    @Test
    public void testDeleteTableData() {

        DeleteResult docs = messageSaveMongoDao.deleteTableData(74860, "74860");
        System.out.println(docs);
    }

    @Test
    public void batchUpdate() {
        String json = "{\n" +
                "\t\"createTime\" : 1675443963555,\n" +
                "\t\"ei\" : 84883,\n" +
                "\t\"fileExt\" : \"jpg\",\n" +
                "\t\"fileName\" : \"64fdd9c6880e03c782b301c4118099ac.jpg\",\n" +
                "\t\"fileSize\" : 13873,\n" +
                "\t\"fromUser\" : \"chenzongxin\",\n" +
                "\t\"fsEa\" : \"84883\",\n" +
                "\t\"keyVersion\" : 4,\n" +
                "\t\"md5sum\" : \"64fdd9c6880e03c782b301c4118099ac\",\n" +
                "\t\"messageId\" : \"12333695114671705844_1675443868464_external\",\n" +
                "\t\"messageTime\" : 1675443864547,\n" +
                "\t\"messageType\" : \"image\",\n" +
                "\t\"npath\" : \"N_202302_04_0f33298f24264c278f31786ce7a89886\",\n" +
                "\t\"sdkFileId\" : \"CqoCMzA4MTkyMDIwMTAyMDQ4MThhMzA4MTg3MDIwMTAwMDIwNDFjZjllZjgwMDIwMzBmNGRmYTAyMDQzNTNkOTQ5ZDAyMDQ2M2RjNTUyMDA0NGM0ZTQ1NTc0OTQ0MzE1ZjMxNjM2NjM5NjU2NjM4MzAzMzM1MzM2NDM5MzQzOTY0MzYzMzY0NjQzMzY1MzkzODMwMzAzMDM2MzEzMDY0Mzk1ZjYzNjYzNzMxNjMzNDMzMzAyZDMyNjQ2MjMyMmQzNDM5NjQzMzJkNjE2MjM0MzYyZDMwMzkzNzM0NjQ2NTMyMzIzOTMzNjMzMTAyMDEwMDAyMDIzNjQwMDQxMDY0ZmRkOWM2ODgwZTAzYzc4MmIzMDFjNDExODA5OWFjMDIwMTAxMDIwMTAwMDQwMBI4TkRkZk1UWTRPRGcxTkRZME1UTTNNekExTmw4eE5UWTFPRGN3TkRZelh6RTJOelUwTkRNNE5qZz0aIDYyMzE2MTYzNjQ2NjM3MzM2NDM5NjQ2NjYyMzIzNzYy\",\n" +
                "\t\"seq\" : 1249,\n" +
                "\t\"toList\" : [\n" +
                "\t\t\"wmQZ1uJQAAP76E4294nLs3QgHvThowFA\"\n" +
                "\t],\n" +
                "\t\"updateTime\" : 1675443963555\n" +
                "}";
        MessageSaveDoc messageSaveDoc = JSONObject.parseObject(json,MessageSaveDoc.class);
        messageSaveDoc.setId(new ObjectId("63dd3efb9948d353e4019eab"));
        System.out.println(messageSaveDoc);
        List<MessageSaveDoc> docList = Lists.newArrayList(messageSaveDoc);
        BulkWriteResult result = messageSaveMongoDao.batchUpdate(84883, docList);
        System.out.println(result);
    }
}
