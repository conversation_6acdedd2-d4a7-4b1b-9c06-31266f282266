package com.facishare.open.qywx.old.accountsync.service

import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData
import com.facishare.fsi.proxy.model.warehouse.a.ATempFileUpload
import com.facishare.fsi.proxy.model.warehouse.a.User
import com.facishare.fsi.proxy.service.AFileStorageService
import com.facishare.open.qywx.accountsync.arg.FileUploadArg
import com.facishare.open.qywx.accountsync.service.FileUploadService
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class FileUploadcServiceTest extends Specification {

    @Autowired
    private FileUploadService fileUploadService;
    @Resource
    private AFileStorageService aFileStorageService;

    def "upload"() {
        given:
        FileUploadArg arg = new FileUploadArg()
        arg.setFsEa(fsEa)
        FileUploadArg.FileItem item = new FileUploadArg.FileItem();
        item.setFileName(fileName)
        item.setUrl(url)
        item.setNpath(npath)
        arg.setFileList(Lists.newArrayList(item))
        arg.setFileType(fileType)
        expect:
        def result = fileUploadService.upload(arg);
        println result;
        where:
        fsEa |   fileName   |   url    |   npath   |   fileType  |  business  |  fileSecurityGroup  |  userId
        "84883" |   "蕾姆"  |    "http://wx.qlogo.cn/mmhead/URAePI6GHmVFLknjPPU7TMjsvbib1jFlq69RnEtcPY3s/0" |  null  |     FileUploadArg.FileTypeEnum.image  |  null  |  null  |  null
        "84883" |   "蕾姆"  |    "https://wework.qpic.cn/wwpic/128897_Cn02VzZFTkeKyz9_1694186224/0" |  null  |     FileUploadArg.FileTypeEnum.image  |  null  |  null  |  null
        "84883" |   "蕾姆"  |    "https://wework.qpic.cn/wwpic/128897_Cn02VzZFTkeKyz9_1694186224/0" |  null  |     FileUploadArg.FileTypeEnum.file  |  null  |  null  |  null
        "84883" |   "蕾姆"  |    null |  "N_201508_01_c9831ab247404688b133b899c28c95b2"  |     FileUploadArg.FileTypeEnum.image  |  null  |  null  |  null
        "84883" |   "蕾姆"  |    null |  "TA_f376c5ee4fe3451ca0218050648cb4a4"  |     FileUploadArg.FileTypeEnum.image  |  "test2"  |  null  |  -10000
    }

    def "query"() {
        given:
        def fsEa = fsEaCase
        def npath = npathCase
        def url = urlCase
        expect:
        def result = fileUploadService.query(fsEa, npath, url);
        println result;
        where:
        fsEaCase  |  npathCase  |  urlCase
        "84883"  |  "N_202311_02_bf33cfdd137d4962b6d1c836310c123f"  |  "https://wework.qpic.cn/wwpic/128897_Cn02VzZFTkeKyz9_1694186224/0"
        "84883"  |  "N_202311_02_93ad11f11d654c54a0d6e7cd473f1513"  |  null
        "84883"  |  null  |  "https://img07.tooopen.com/********/tooopen_sy_201253432288.jpg"
        "84883"  |  null  |  "https://wework.qpic.cn/wwhead/duc2TvpEgSQl4wxMPWAVticrdLFAyjq8AXIaBr1Tia1OHNHZ3SdjkGPaCOR1x5OIibflO1icgRA9exI/0"
    }

    def "apathDownFile"() {
        given:
        ATempFileUpload.Arg arg = new ATempFileUpload.Arg()
        User user = new User()
        user.setEmployId(userId)
        user.setEnterpriseAccount("84883")
        arg.setUser(user)
        URL url = new URL("https://wework.qpic.cn/wwhead/duc2TvpEgSQl4wxMPWAVticrdLFAyjq8AXIaBr1Tia1OHNHZ3SdjkGPaCOR1x5OIibflO1icgRA9exI/0")
        InputStream inputStream = url.openStream()
        URLConnection connection = url.openConnection()
        connection.connect()
        String contentLength = connection.getHeaderField("Content-Length")
        // 创建一个 byte 数组输出流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()
        BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)
        // 读取数据到缓冲区，然后写入到 byte 数组输出流中
        byte[] buffer = new byte[1024]
        int bytesRead
        while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead)
        }

        // 将 byte 数组输出流中的数据写入到 byte 数组中
        byte[] data = byteArrayOutputStream.toByteArray()

        // 关闭输入流和 byte 数组输出流
        inputStream.close()
        byteArrayOutputStream.close()
        bufferedInputStream.close()
        arg.setData(data)
        arg.setBusiness(business)
        arg.setFileSecurityGroup(fileSecurityGroup)
        expect:
        def result = aFileStorageService.tempFileUpload(arg)
        println result
        where:
        business  |  fileSecurityGroup  |  userId
        null  |  null  | -10000                            //报错：Method threw 'com.facishare.fsi.proxy.exception.FsiProxyException' exception. Cannot evaluate com.sun.proxy.$Proxy144.toString()
        "test1"  |  "test1"  | -10000                      //ATempFileUpload.Result{tempFileName='TA_afe1b34876544d4fb3d9fa687ae4e115'}
        "test2"  |  null  |  -10000                        //ATempFileUpload.Result{tempFileName='TA_f376c5ee4fe3451ca0218050648cb4a4'}
        null  |  "test3"  |  -10000                        //报错：Method threw 'com.facishare.fsi.proxy.exception.FsiProxyException' exception. Cannot evaluate com.sun.proxy.$Proxy144.toString()
        "test4"  |  "test4"  | 1000                        //ATempFileUpload.Result{tempFileName='TA_680ad2ac90b3485a8a4b877f369468ef'}
        "test5"  |  "test5"  | null                        //报错：Method threw 'com.facishare.fsi.proxy.exception.FsiProxyException' exception. Cannot evaluate com.sun.proxy.$Proxy144.toString()
    }

    def "getFileMetaData"() {
        given:
        AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg()
        arg.setFileName(fileName)
        arg.setFileSecurityGroup(fileSecurityGroup)
        arg.setBusiness(business)
        arg.setUser()
        User user = new User()
        user.setEmployId(userId)
        user.setEnterpriseAccount("84883")
        arg.setUser(user)
        expect:
        def result = null
        try {
            result = aFileStorageService.getFileMetaData(arg)
        } catch (Exception e) {
            print(e)
        }
        println result
        where:
        fileName  |  fileSecurityGroup  |  business  |  userId
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  null  |  null  |  1000                  //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE AGetFileMetaDataArg{business=null, fileName=TA_afe1b34876544d4fb3d9fa687ae4e115, user=User{employId='1000', enterpriseAccount='84883'}, fileSecurityGroup=null},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  null  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE AGetFileMetaDataArg{business=null, fileName=TA_afe1b34876544d4fb3d9fa687ae4e115, user=User{employId='1000', enterpriseAccount='84883'}, fileSecurityGroup=test1},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  null  |  "test1"  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10005,Message:business Error Code:NO_FILE_PERMISSION_CODE tempFileName:TA_afe1b34876544d4fb3d9fa687ae4e115;user:UserEntity{employId='1000'enterpriseAccount='84883'},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  "test1"  |  1000            //148787
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  "test1"  |  -10000          //148787
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  "test1"  |  null            //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE AGetFileMetaDataArg{business=test1, fileName=TA_afe1b34876544d4fb3d9fa687ae4e115, user=User{employId='null', enterpriseAccount='84883'}, fileSecurityGroup=test1},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  "test2"  |  "test2"  |  1000            //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10005,Message:business Error Code:NO_FILE_PERMISSION_CODE tempFileName:TA_f376c5ee4fe3451ca0218050648cb4a4;user:UserEntity{employId='1000'enterpriseAccount='84883'},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  "test2"  |  null  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE AGetFileMetaDataArg{business=null, fileName=TA_f376c5ee4fe3451ca0218050648cb4a4, user=User{employId='1000', enterpriseAccount='84883'}, fileSecurityGroup=test2},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  null  |  "test2"  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10005,Message:business Error Code:NO_FILE_PERMISSION_CODE tempFileName:TA_f376c5ee4fe3451ca0218050648cb4a4;user:UserEntity{employId='1000'enterpriseAccount='84883'},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/AGetFileMetaData
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  null  |  "test2"  |  -10000             //148787
    }

    def "downloadFile"() {
        given:
        ADownloadFile.Arg arg = new ADownloadFile.Arg()
        arg.setaPath(apath)
        arg.setFileSecurityGroup(fileSecurityGroup)
        arg.setBusiness(business)
        arg.setUser()
        User user = new User()
        user.setEmployId(userId)
        user.setEnterpriseAccount("84883")
        arg.setUser(user)
        expect:
        def result = null
        try {
            result = aFileStorageService.downloadFile(arg)
        } catch (Exception e) {
            print(e)
        }
        println result
        where:
        apath  |  fileSecurityGroup  |  business  |  userId
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  null  |  null  |  1000                  //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE null,url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  null  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE null,url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  null  |  "test1"  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10005,Message:business Error Code:NO_FILE_PERMISSION_CODE tempFileName:TA_afe1b34876544d4fb3d9fa687ae4e115;user:UserEntity{employId='1000'enterpriseAccount='84883'},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  "test1"  |  1000            //result
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  "test1"  |  -10000          //result
        "TA_afe1b34876544d4fb3d9fa687ae4e115"  |  "test1"  |  "test1"  |  null            //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE null,url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  "test2"  |  "test2"  |  1000            //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10005,Message:business Error Code:NO_FILE_PERMISSION_CODE tempFileName:TA_f376c5ee4fe3451ca0218050648cb4a4;user:UserEntity{employId='1000'enterpriseAccount='84883'},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  "test2"  |  null  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10003,Message:business Error Code:REQUEST_ARG_ERROR_CODE null,url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  null  |  "test2"  |  1000               //报错：com.facishare.fsi.proxy.exception.FsiClientException: FsiFailure,Code:10005,Message:business Error Code:NO_FILE_PERMISSION_CODE tempFileName:TA_f376c5ee4fe3451ca0218050648cb4a4;user:UserEntity{employId='1000'enterpriseAccount='84883'},url:http://172.31.100.247:32697/fs-warehouse-cross/core/A/AStorage/ADownloadFile
        "TA_f376c5ee4fe3451ca0218050648cb4a4"  |  null  |  "test2"  |  -10000             //result
        "A_202311_09_37b3ab905d6b4133aa43bf7d9bc837ed"  |  null  |  "test5"  |  1001      //result
    }
}
