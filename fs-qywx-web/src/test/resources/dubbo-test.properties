#dubbo.registry.file=${user.home}/.dubbo/fs-open-qywx-accountsync-test.cache
#dubbo.registry.address=N/A
#duboo.port=20880
#oaDubboConfigGroup=fs-open-qywx-test
#
## æµè¯ç¯å¢éç½®
#spring.profiles.active=test
#config.remote.enabled=false
#config.env=test
#
## æ°æ®åºéç½®
#fs.open.qywx.db.url=************************************************************************************
#fs.open.qywx.db.username=root
#fs.open.qywx.db.password=root
#fs.open.qywx.db.driverClassName=com.mysql.jdbc.Driver
#
## Rediséç½®
#redis.host=localhost
#redis.port=6379
#redis.database=0
#redis.password=
#
## MQéç½®
#rocketmq.namesrv.addr=localhost:9876
#rocketmq.producer.group=fs-open-qywx-test-producer
#rocketmq.consumer.group=fs-open-qywx-test-consumer