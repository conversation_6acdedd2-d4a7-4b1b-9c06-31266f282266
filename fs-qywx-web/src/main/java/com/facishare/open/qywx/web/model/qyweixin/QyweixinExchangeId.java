package com.facishare.open.qywx.web.model.qyweixin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Create by max on 2020/05/21
 **/
@Data
public class QyweixinExchangeId {

    @Data
    public static class Argument implements Serializable {

        private static final long serialVersionUID = -5415983969483045177L;

        /**
         * 1:部门。2:员工
         */
        private int type = 2;

        private String ea;

        private List<Integer> fsIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Result implements Serializable {

        private static final long serialVersionUID = -8097748596711726205L;

        private Integer fsId;

        private String openId;
    }
}