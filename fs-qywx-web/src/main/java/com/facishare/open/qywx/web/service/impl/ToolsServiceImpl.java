package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.QyweixinIdToOpenidManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.QyweixinFileManager;
import com.facishare.open.oa.base.dbproxy.pg.params.*;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseExtendModel;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.CreateConnectorArg;
import com.facishare.open.qywx.accountinner.model.FsAccountModel;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.arg.BatchCreateQywxConnectorArg;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.*;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.db.dao.*;
import com.facishare.open.qywx.web.entity.ErpConnectInfoEntity;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.entity.entity.MessageGeneratingPo;
import com.facishare.open.qywx.web.enums.PricePlanTypeEnum;
import com.facishare.open.qywx.web.info.ConversationArchiveInfo;
import com.facishare.open.qywx.web.manager.*;
import com.facishare.open.qywx.web.model.qyweixin.*;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("toolsService")
@Slf4j
// IgnoreI18nFile
public class ToolsServiceImpl implements ToolsService {
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private EIEAConverter eieaConverter;
    @ReloadableProperty("repAppId")
    private String repAppId;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private FsManager fsManager;
//    @Autowired
//    private QyweixinIdToOpenidManager qyweixinIdToOpenidManager;
//    @Autowired
//    private OutUserInfoManger outUserInfoManger;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Resource
    private FsObjServiceProxy fsObjServiceProxy;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EventCloudProxyManager eventCloudProxyManager;
    @Autowired
    private ErpdssManager erpdssManager;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Resource
    private OuterOaExternalContactsManager outerOaExternalContactsManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Resource
    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;
    @Resource
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;
    @Resource
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Resource
    private QyweixinBusinessBindInfoDao qyweixinBusinessInfoBindDao;
    @Resource
    private QyweixinBusinessInfoBindManager qyweixinBusinessInfoBindManager;
    @Autowired
    private QyweixinIdToOpenidDao qyweixinIdToOpenidDao;  // MySQL的DAO

    @Resource
    private QyweixinIdToOpenidManager qyweixinIdToOpenidManager;

    @Resource
    private QyweixinFileDao qyweixinFileDao; // MySQL的DAO

    @Resource
    private QyweixinFileManager qyweixinFileManager; // PG的Manager
    @Resource
    private QyweixinExternalContactDao qyweixinExternalContactDao;

    @Resource
    private QyweixinCorpBindDao qyweixinCorpBindDao; // 添加MySQL的DAO

    @Autowired
    private QyweixinAccountEmployeeBindDao accountEmployeeBindDao;

    @Autowired
    private QyweixinAccountDepartmentBindDao accountDepartmentBindDao;

    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;

    @Resource
    private MessageGeneratingDao messageGeneratingDao;

    @Resource
    private QyweixinAccountEnterpriseBindDao qyweixinAccountEnterpriseBindDao;

    @Resource QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Override
    public Result<AppLicenseInfo> getAppLicenseInfo(String corpId,String appId) {
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        com.facishare.open.qywx.accountinner.result.Result<AppLicenseInfo> appLicenseInfoResult = qyWeixinManager.getAppLicenseInfo(corpId, appId);
        if(appLicenseInfoResult.isSuccess()) {
            return new Result<>(appLicenseInfoResult.getData());
        }
        return new Result<>(appLicenseInfoResult.getCode(), appLicenseInfoResult.getMsg(),null);
    }

    @Override
    public Result<List<String>> getUserListInAppVisibleRange(String corpId, String appId) {
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        com.facishare.open.qywx.accountinner.result.Result<List<String>> userIdListResult = qyWeixinManager.getUserListInAppVisibleRange(corpId, appId);
        if(!userIdListResult.isSuccess() || ObjectUtils.isEmpty(userIdListResult.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), userIdListResult.getMsg(),null);
        }
        return new Result<>(userIdListResult.getData());
    }

    @Override
    public Result<String> corpId2OpenCorpId(String corpId) {
        String openCorpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        return new Result<>(openCorpId);
    }

    @Deprecated
    @Override
    public Result<String> userId2OpenUserId(String corpId, String userId) {
        return userId2OpenUserId2(corpId, ConfigCenter.crmAppId, userId);
    }

    @Override
    public Result<String> userId2OpenUserId2(String corpId, String appId, String userId) {
        appId = StringUtils.isEmpty(appId) ? ConfigCenter.crmAppId : appId;
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> listResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId, appId);
        String openUserId = userId;
        if(listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
            openUserId = listResult.getData().get(0).getOpen_userid();
        }
        return new Result<>(openUserId);
    }

    @Override
    public Result<String> userId2OpenUserId3(String corpId, String appId, String userId) {
        appId = StringUtils.isEmpty(appId) ? ConfigCenter.crmAppId : appId;
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> result = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId, appId);
        String openUserId = userId;
        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            openUserId = result.getData().get(0).getOpen_userid();
        }
        if(StringUtils.equalsIgnoreCase(result.getCode(),"0")) {
            return new Result<>(openUserId);
        }
        return new Result<>(result.getCode(),result.getMsg(),openUserId);
    }

//    @Override
//    public Result<Integer> refreshEmployeeTable(String corpId) {
//        new Thread(() -> this.refreshEmployeeTable2(corpId)).start();
//        return new Result<>();
//    }
//
//    private Result<Integer> refreshEmployeeTable2(String corpId) {
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.selectAll(corpId);
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        if(CollectionUtils.isEmpty(result.getData())) {
//            if(StringUtils.isNotEmpty(corpId2)) {
//                result = qyweixinAccountBindService.selectAll(corpId2);
//                if(CollectionUtils.isNotEmpty(result.getData())) {
//                    corpId = corpId2;
//                }
//            }
//        }
//        log.info("ToolsServiceImpl.refreshEmployeeTable,corpId={},result={}",corpId,result);
//        if(CollectionUtils.isEmpty(result.getData())) return new Result<>(0);
//
//        List<QyweixinAccountEmployeeMapping> totalList = new ArrayList<>();
//        for(QyweixinAccountEmployeeMapping mapping : result.getData()) {
//            String userId = mapping.getOutAccount();
//            try {
//                if(!corpId2.equals(mapping.getOutEa())) {
//                    mapping.setOutEa(corpId2);
//                }
//                //先查询下企微用户详情，获取到才继续
//                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfo(ConfigCenter.crmAppId, corpId, userId);
//                log.info("ToolsServiceImpl.refreshEmployeeTable,userInfoResult={}",userInfoResult);
//                if(!userInfoResult.isSuccess()) {
//                    continue;
//                }
//                com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> listResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId);
//                log.info("ToolsServiceImpl.refreshEmployeeTable,list={}",listResult);
//                if(listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
//                    mapping.setOutAccount(listResult.getData().get(0).getOpen_userid());
//                    mapping.setIsvAccount(mapping.getOutAccount());
//                    totalList.add(mapping);
//                }
//            } catch (Exception e) {
//                log.info("ToolsServiceImpl.refreshEmployeeTable,exception={}",e.getMessage());
//            } finally {
//                //停一会再调用
//                try {
//                    TimeUnit.SECONDS.sleep(1);
//                } catch (InterruptedException e) {
//                    log.info("ToolsServiceImpl.refreshEmployeeTable,exception={}",e.getMessage());
//                }
//            }
//        }
//
//        log.info("ToolsServiceImpl.refreshEmployeeTable,totalList={},totalList.size={},result.data.size={}",
//                totalList,totalList.size(),result.getData().size());
//        com.facishare.open.qywx.accountbind.result.Result<Integer> result2 = qyweixinAccountBindService.batchUpdateEmployeeMapping(totalList);
//        return new Result<>(result2.getData());
//    }

//    @Override
//    public Result<Integer> refreshEnterpriseTable(String corpId) {
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = null;
//        //明文的corpId获取到的企业信息
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.selectEnterpriseBind("qywx", corpId);
//        //刷企业表的数据，只用刷out_ea字段，明文转密文，对于isv_out_ea字段，有值不用管，没值直接插入密文
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        if(CollectionUtils.isNotEmpty(result.getData()) && StringUtils.isNotEmpty(corpId2)) {
//            enterpriseMappingList = result.getData();
//            for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
//                enterpriseMapping.setOutEa(corpId2);
//                enterpriseMapping.setIsvOutEa(corpId2);
//            }
//        } else {
//            result = qyweixinAccountBindService.selectEnterpriseBind("qywx", corpId2);
//            if(CollectionUtils.isNotEmpty(result.getData()) && StringUtils.isNotEmpty(corpId2)) {
//                enterpriseMappingList = result.getData();
//                for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
//                    enterpriseMapping.setIsvOutEa(corpId2);
//                }
//            }
//        }
//        log.info("ToolsServiceImpl.refreshEnterpriseTable,corpId={},enterpriseMappingList={}",corpId,enterpriseMappingList);
//        if(CollectionUtils.isEmpty(enterpriseMappingList)) return new Result<>(0);
//        int count = 0;
//        for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
//            com.facishare.open.qywx.accountbind.result.Result<Integer> result2 = qyweixinAccountBindService.updateQyweixinAccountEnterpriseMapping(enterpriseMapping);
//            count+=result2.getData();
//        }
//        return new Result<>(count);
//    }

//    @Override
//    public Result<Integer> refreshApplicationTable(String corpId) {
//        int count = 0;
//        QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
//        qyweixinCorpBindBo.setCorpId(corpId);
//        List<QyweixinCorpBindBo> entity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        qyweixinCorpBindBo.setCorpId(corpId2);
//        List<QyweixinCorpBindBo> entity2 = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
//        List<QyweixinCorpBindBo> entity3 = new LinkedList<>();
//        if(CollectionUtils.isNotEmpty(entity)) entity3.addAll(entity);
//        if(CollectionUtils.isNotEmpty(entity2)) entity3.addAll(entity2);
//        if(CollectionUtils.isEmpty(entity3)) return new Result<>();
//        for (int i = 0; i < entity3.size(); i++) {
//            //无论原先明文还是密文，直接刷成密文
//            entity3.get(i).setCorpId(corpId2);
//            entity3.get(i).setIsvCorpId(corpId2);
//            int update = qyweixinCorpBindDao.update(entity3.get(i));
//            count += update;
//        }
//        return new Result<>(count);
//    }
//
//    @Override
//    public Result refreshEnterpriseAccount(String corpId, String agentId) {
//        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.enterpriseBind2OpenEnterpriseBind(corpId, agentId);
//        if(StringUtils.isNotEmpty(result.getData()) && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
//            return new Result<>().addError("s120050000", JSONPath.read(result.getData(), "$.errmsg").toString(),null);
//        }
//        return new Result<>().addError(JSONPath.read(result.getData(), "$.errcode").toString(), JSONPath.read(result.getData(), "$.errmsg").toString(),null);
//    }

    @Override
    public Result<Void> getEnterpriseAccountMigration(String corpId, String appId) {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getEnterpriseAccountMigration(corpId, appId);
        if(StringUtils.isNotEmpty(result.getData()) && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
            return new Result<Void>().addError("s120050000", JSONPath.read(result.getData(), "$.migration_info").toString(),null);
        }
        return new Result<Void>().addError(JSONPath.read(result.getData(), "$.errcode").toString(), JSONPath.read(result.getData(), "$.errmsg").toString(),null);
    }

//    @Override
//        public Result<Integer> refreshDepartmentTable(String corpId) {
//            String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//            if(StringUtils.isEmpty(corpId2)) return new Result<>();
//            com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.batchUpdateDepartmentBind(corpId, corpId2);
//            return new Result<>(result.getData());
//    }
//
//    @Override
//    public Result<Integer> refreshOrderTable(String corpId) {
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        if(StringUtils.isEmpty(corpId2)) return new Result<>();
//        int count = qyweixinOrderInfoDao.BatchUpdateDepartmentBind(corpId, corpId2);
//        return new Result<>(count);
//    }
//
//    @Override
//    public Result<Integer> refreshApplicationInfoTable(String corpId) {
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        if(StringUtils.isEmpty(corpId2)) return new Result<>();
//        QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
//        qyweixinCorpInfoBo.setCorpId(corpId);
//        List<QyweixinCorpInfoBo> result = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
//        log.info("ToolsServiceImpl.refreshApplicationInfoTable,corpId={},result={}",corpId,result);
//        if(CollectionUtils.isEmpty(result)) return new Result<>(0);
//        int count = 0;
//        for(QyweixinCorpInfoBo mapping : result) {
//            String userId = mapping.getUserId();
//            try {
//                com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> listResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId2);
//                log.info("ToolsServiceImpl.refreshApplicationInfoTable,list={}",listResult);
//                //安装CRM的管理员会离职，但是没有进行更新操作，会导致取不到值,取不到值直接使用原userId
//                count = qyweixinCorpInfoDao.batchUpdateApplicationInfoBind(corpId, corpId2, userId, (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) ? listResult.getData().get(0).getOpen_userid() : userId);
//            } catch (Exception e) {
//                log.info("ToolsServiceImpl.refreshApplicationInfoTable,exception={}",e.getMessage());
//            }
//        }
//        return new Result<>(count);
//    }

//    @Override
//    public Result<Integer> deleteExternalContactTable(String corpId) {
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        int count = qyweixinExternalContactDao.deleteAllExternalUserIds(corpId, corpId2);
//        return new Result<>(count);
//    }
//
//    @Override
//    public Result<Integer> deleteUserTable(String corpId) {
//        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        int count = qyweixinUserDao.deleteAllUserIds(corpId, corpId2);
//        return new Result<>(count);
//    }

//    @Override
//    public Result<Map<String, Object>> refreshAllEnterpriseAccount(List<String> corpIds) {
//        Map<String, Object> mapResult = new HashMap<>();
//        for (int i = 0; i < corpIds.size(); i++) {
//            Map<String, Object> map = new HashMap<>();
//            String corpId = corpIds.get(i);
//            //刷新企业绑定表：enterprise_account_bind
//            Result<Integer> enterpriseAccountBind = this.refreshEnterpriseTable(corpId);
//            map.put("enterpriseAccountBind", enterpriseAccountBind);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},enterpriseAccountBind={}",corpId, enterpriseAccountBind);
//            //刷新员工绑定表：employee_account_bind
//            Result<Integer> employeeAccountBind = this.refreshEmployeeTable(corpId);
//            map.put("employeeAccountBind", employeeAccountBind);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},employeeAccountBind={}",corpId, employeeAccountBind);
//            //刷新应用绑定表：qyweixin_corp_bind
//            Result<Integer> qyweixinCorpBind = this.refreshApplicationTable(corpId);
//            map.put("qyweixinCorpBind", qyweixinCorpBind);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},qyweixinCorpBind={}",corpId, qyweixinCorpBind);
//            //刷新部门绑定表：department_account_bind
//            Result<Integer> departmentAccountBind = this.refreshDepartmentTable(corpId);
//            map.put("departmentAccountBind", departmentAccountBind);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},departmentAccountBind={}",corpId, departmentAccountBind);
//            //刷新订单表：qyweixin_order_info
//            Result<Integer> qyweixinOrderInfo = this.refreshOrderTable(corpId);
//            map.put("qyweixinOrderInfo", qyweixinOrderInfo);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},qyweixinOrderInfo={}",corpId, qyweixinOrderInfo);
//            //刷新应用信息表：qyweixin_corp_info
//            Result<Integer> qyweixinCorpInfo = this.refreshApplicationInfoTable(corpId);
//            map.put("qyweixinCorpInfo", qyweixinCorpInfo);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},qyweixinCorpInfo={}",corpId, qyweixinCorpInfo);
//            //迁移企业账号,第三方下的所有应用
//            com.facishare.open.qywx.accountinner.result.Result<String> refreshCRM = qyWeixinManager.enterpriseBind2OpenEnterpriseBind(corpId, "");
//            map.put("refreshCRM", refreshCRM.getData());
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},refreshCRM={}",corpId, refreshCRM);
//            String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//            //迁移代开发应用
//            QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
//            qyweixinCorpBindBo.setCorpId(corpId);
//            qyweixinCorpBindBo.setAppId(repAppId);
//            List<QyweixinCorpBindBo> entity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
//            if(CollectionUtils.isEmpty(entity)) {
//                qyweixinCorpBindBo.setCorpId(corpId2);
//                entity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
//            }
//            com.facishare.open.qywx.accountinner.result.Result<String> refreshRep = null;
//            if(CollectionUtils.isNotEmpty(entity)) {
//                refreshRep = qyWeixinManager.enterpriseBind2OpenEnterpriseBind(corpId, entity.get(0).getAgentId());
//            }
//            map.put("refreshRep", refreshRep.getData());
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},refreshRep={}",corpId, refreshRep);
//            //查询企业CRM迁移状态,迁移完成企业后，需要使用密文获取token
//            com.facishare.open.qywx.accountinner.result.Result<String> queryCRMRefreshStatus = qyWeixinManager.getEnterpriseAccountMigration(corpId2, ConfigCenter.crmAppId);
//            map.put("queryCRMRefreshStatus", queryCRMRefreshStatus.getData());
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},queryCRMRefreshStatus={}",corpId, queryCRMRefreshStatus);
//            //查询企业代开发迁移状态,迁移完成企业后，需要使用密文获取token
//            com.facishare.open.qywx.accountinner.result.Result<String> queryRepRefreshStatus = qyWeixinManager.getEnterpriseAccountMigration(corpId2, repAppId);
//            map.put("queryRepRefreshStatus", queryRepRefreshStatus.getData());
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},queryRepRefreshStatus={}",corpId, queryRepRefreshStatus);
//
//            //有团队依赖这两个表，不能直接删除这两个表的数据
////            //删除外部联系人缓存表：qyweixin_external_contact
////            Result<Integer> result10 = this.deleteExternalContactTable(corpId);
////            map.put("result10", result10);
////            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},result10={}",corpId, result10);
////            //删除用户缓存表：qyweixin_user
////            Result<Integer> result11 = this.deleteUserTable(corpId);
////            map.put("result11", result11);
////            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},result11={}",corpId, result11);
//
//            mapResult.put(corpId, map);
//        }
//        return new Result<>(mapResult);
//    }

//    @Override
//    public Result<List<QyweixinAccountEmployeeMapping>> updateServiceProviderEmployeeBind(String fsEa, String appId, String outEa) {
//        List<QyweixinAccountEmployeeMapping> errorUpdateEmpResult = new LinkedList<>();
//        if(StringUtils.isEmpty(appId)) {
//            appId = ConfigCenter.crmAppId;
//        }
//        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
//                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, appId, outEa);
//        log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,qyweixinAccountEmployeeMappings={}", qyweixinAccountEmployeeMappings);
//        if(CollectionUtils.isEmpty(qyweixinAccountEmployeeMappings)) {
//            return new Result<>(errorUpdateEmpResult);
//        }
//        for(QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping : qyweixinAccountEmployeeMappings) {
//            if(qyweixinAccountEmployeeMapping.getIsvAccount().length() == 32) {
//                log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,qyweixinAccountEmployeeMapping={}", qyweixinAccountEmployeeMapping);
//                continue;
//            }
//            qyweixinAccountEmployeeMapping.setOutAccount(qyweixinAccountEmployeeMapping.getIsvAccount());
//            try {
//                int i = qyweixinAccountBindInnerService.updateQyweixinAccountEmployee(qyweixinAccountEmployeeMapping);
//            } catch (Exception e) {
//                log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,qyweixinAccountEmployeeMapping1={}", qyweixinAccountEmployeeMapping);
//                errorUpdateEmpResult.add(qyweixinAccountEmployeeMapping);
//            }
//        }
//        log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,errorUpdateEmpResult={}", errorUpdateEmpResult);
//        return new Result<>(errorUpdateEmpResult);
//    }

    @Override
    public Result<List<FsAccountModel>> getAccountInfo(int bindType, List<String> accountLevelList) {
        String key = "key_account_bind_info_list";
        String value = redisDataSource.getRedisClient().get(key);
        List<FsAccountModel> fsAccountModelList = null;
        if(StringUtils.isNotEmpty(value)) {
            fsAccountModelList = JSONObject.parseArray(value,FsAccountModel.class);
            return new Result<>(fsAccountModelList);
        }
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).bindType(BindTypeEnum.manual).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            log.info("ToolsServiceImpl.getAccountInfo,result is empty");
            return new Result<>();
        }
        List<String> fsEaList = enterpriseBindEntities.stream().map(OuterOaEnterpriseBindEntity::getFsEa).collect(Collectors.toList());
        log.info("ToolsServiceImpl.getAccountInfo,fsEaList={}",fsEaList);
        fsAccountModelList = queryAccountInfoList(fsEaList);
        log.info("ToolsServiceImpl.getAccountInfo,fsAccountModelList={}",fsAccountModelList);
        List<FsAccountModel> dataList = new ArrayList<>();
        for(FsAccountModel model : fsAccountModelList) {
            if(accountLevelList.contains(model.getAccountLevel())) {
                String accountLevel = model.getAccountLevel();
                if(StringUtils.equalsIgnoreCase(accountLevel,"1")) {
                    accountLevel = "SVIP";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"2")) {
                    accountLevel = "VIP";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"3")) {
                    accountLevel = "重要";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"4")) {
                    accountLevel = "标准";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"5")) {
                    accountLevel = "小微";
                } else {
                    accountLevel = "其它";
                }
                model.setAccountLevel(accountLevel);
                dataList.add(model);
            }
        }
        String jsonData = JSONObject.toJSONString(dataList);
        log.info("ToolsServiceImpl.getAccountInfo,dataList={}",jsonData);
        redisDataSource.getRedisClient().set(key,jsonData);
        redisDataSource.getRedisClient().expire(key,60 * 60L);
        return new Result<>(dataList);
    }

    @Override
    public List<FsAccountModel> queryAccountInfoList(List<String> fsEaList) {
        String eaFieldApiName = "UDSText6__c";
        String accountLevelFieldApiName = "UDSSel31__c";
        String csmFieldApiName = "field_80ZX1__c";

//        String eaFieldApiName = "field_1kyP6__c";
//        String accountLevelFieldApiName = "field_4Nsb1__c";
//        String csmFieldApiName = "field_44M24__c";

        Filter filter=new Filter();
        filter.setFieldName(eaFieldApiName);//企业帐号字段apiName
        filter.setOperator("IN");
        filter.setFieldValues(fsEaList);

        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(10000);
        searchQuery.setFilters(Lists.newArrayList(filter));

        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName("AccountObj");
        controllerListArg.setSearchQuery(searchQuery);

        int ei = eieaConverter.enterpriseAccountToId("fs");
        HeaderObj headerObj = new HeaderObj(ei, CrmConstants.SYSTEM_USER);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj,
                "AccountObj",
                controllerListArg);
        log.info("ToolsServiceImpl.queryAccountInfoList,dataList.size={},listResult={}",listResult.getData().getDataList().size(),JSONObject.toJSONString(listResult));

        List<FsAccountModel> fsAccountModelList = new ArrayList<>();
        if(listResult.isSuccess() && listResult.getData().getDataList().size()>0) {
            for(ObjectData objectData : listResult.getData().getDataList()) {
                log.info("ToolsServiceImpl.queryAccountInfoList,objectData={}",JSONObject.toJSONString(objectData));
                String fsEa = objectData.getString(eaFieldApiName);//企业帐号字段apiName
                String accountName = objectData.getName();
                String accountLevel = objectData.getString(accountLevelFieldApiName);
                String accountOwner = objectData.getOwnerName();
                Map<String,Object> csmMap = (Map<String,Object>)objectData.get(csmFieldApiName+"__r");
                Object csm = null;
                if(csmMap!=null) {
                    csm = csmMap.get("name");
                } else {
                    log.info("ToolsServiceImpl.queryAccountInfoList,csmMap=null");
                }
                log.info("ToolsServiceImpl.queryAccountInfoList,fsEa={},accountLevel={},accountOwner={},csm={}",fsEa,accountLevel,accountOwner,csm);
                FsAccountModel fsAccountModel = new FsAccountModel();
                fsAccountModel.setFsEa(fsEa);
                fsAccountModel.setFsEn(accountName);
                fsAccountModel.setAccountLevel(accountLevel);
                fsAccountModel.setAccountOwner(accountOwner);
                fsAccountModel.setCsm(csm!=null ? csm.toString() : null);
                fsAccountModel.setBindType(1);
                fsAccountModelList.add(fsAccountModel);
            }
        }
        return fsAccountModelList;
    }

    @Override
    public Result<QyweixinAppStatusInfo> getQYWXAppBindInfo(String corpId, String appId) {
        if (StringUtils.isBlank(corpId) || StringUtils.isBlank(appId))
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        QyweixinAppStatusInfo info = new QyweixinAppStatusInfo();
        //先查数据库
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        if(ObjectUtils.isEmpty(oaAppInfoEntity)) {
            return new Result<>();
        }
        info.setCorpId(corpId);
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);

        info.setName(qyweixinAppInfoParams.getAuthCorpInfo().getCorpName());
        info.setStatus(oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.normal ? 0 : 1);
        //如果停用了，就不用继续查了
        if(oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop) {
            return new Result<>(info);
        }
        //再查应用信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(corpId, appId);
        //如果查询为空或者状态异常，状态为1
        if(!appInfoResult.isSuccess() || ObjectUtils.isEmpty(appInfoResult.getData())) {
            info.setAppStatus(1);
        } else {
            AppInfo appInfo = appInfoResult.getData();
            info.setAppStatus(0);
            info.setClose(appInfo.getClose());
            info.setCustomizedPublishStatus(appInfo.getCustomized_publish_status());
        }
        return new Result<>(info);
    }

    @Override
    public Result<QyweixinAppAuthorityInfo> getQYWXRepAppAuthorityInfo(String corpId) {
        //查询代开发应用信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(corpId, repAppId);
        if(!appInfoResult.isSuccess() || ObjectUtils.isEmpty(appInfoResult.getData())) {
            return new Result<>();
        }
        AppInfo appInfo = appInfoResult.getData();
        QyweixinAppAuthorityInfo info = new QyweixinAppAuthorityInfo();
        info.setCorpId(corpId);
        info.setName(appInfo.getName());
        if(ObjectUtils.isNotEmpty(appInfo.getAllow_userinfos()) && CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfo(repAppId, corpId, appInfo.getAllow_userinfos().getUser().get(0).getUserid());
            if(userInfoResult.isSuccess() && ObjectUtils.isNotEmpty(userInfoResult.getData())) {
                QyweixinUserDetailInfoRsp userInfo = userInfoResult.getData();
                if(StringUtils.isNotEmpty(userInfo.getName()) && !userInfo.getName().equals(userInfo.getUserid())) {
                    info.setUserStatus(0);
                } else {
                    info.setUserStatus(1);
                }
            } else {
                info.setUserStatus(1);
            }
        }
        if(ObjectUtils.isNotEmpty(appInfo.getAllow_partys()) && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
            if(info.getUserStatus() == null) {
                for(String depId : appInfo.getAllow_partys().getPartyid()) {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(repAppId,
                            corpId, depId);
                    if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
                        QyweixinDepartmentEmployeeListRsp departmentEmployeeList = departmentEmployeeListResult.getData();
                        if(StringUtils.isNotEmpty(departmentEmployeeList.getUserlist().get(0).getName()) && !departmentEmployeeList.getUserlist().get(0).getName().equals(departmentEmployeeList.getUserlist().get(0).getUserid())) {
                            info.setUserStatus(0);
                        } else {
                            info.setUserStatus(1);
                        }
                    } else {
                        info.setUserStatus(1);
                    }
                    break;
                }
            }
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(repAppId, corpId, appInfo.getAllow_partys().getPartyid().get(0));
            if(departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
                QyweixinDepartmentInfoRsp departmentInfo = departmentInfoResult.getData();
                if(StringUtils.isNotEmpty(departmentInfo.getDepartment().getName()) && !departmentInfo.getDepartment().getName().equals(departmentInfo.getDepartment().getId())) {
                    info.setDepartmentStatus(0);
                } else {
                    info.setDepartmentStatus(1);
                }
            } else {
                info.setDepartmentStatus(1);
            }
        }
        if(info.getUserStatus() == null || info.getDepartmentStatus() == null) {
            if(ObjectUtils.isNotEmpty(appInfo.getAllow_tags()) && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
                //查询标签
                for(String tagId : appInfo.getAllow_tags().getTagid()) {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = qyWeixinManager.getTagEmployeeList(repAppId, corpId, tagId);
                    if(tagEmployeeListRspResult.isSuccess() && tagEmployeeListRspResult.getData()!=null) {
                        QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
                        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(tagEmployeeListRsp.getUserlist())) {
                            //还需要调用人员详情接口
                            if(info.getUserStatus() == null) {
                                for(QyweixinUserDetailInfoRsp userDetailInfoRsp : tagEmployeeListRsp.getUserlist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> updateUserDetailInfoRspResult = qyWeixinManager.getUserInfo(repAppId, corpId, userDetailInfoRsp.getUserid());
                                    if(updateUserDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(updateUserDetailInfoRspResult.getData())) {
                                        QyweixinUserDetailInfoRsp updateUserDetailInfoRsp = updateUserDetailInfoRspResult.getData();
                                        if(StringUtils.isNotEmpty(updateUserDetailInfoRsp.getName()) && !updateUserDetailInfoRsp.getName().equals(updateUserDetailInfoRsp.getUserid())) {
                                            info.setUserStatus(0);
                                        } else {
                                            info.setUserStatus(1);
                                        }
                                    } else {
                                        info.setUserStatus(1);
                                    }
                                    break;
                                }
                            }
                        }
                        if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getPartylist())) {
                            if(info.getUserStatus() == null) {
                                for(String depId : tagEmployeeListRsp.getPartylist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(repAppId,
                                            corpId, depId);
                                    if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
                                        QyweixinDepartmentEmployeeListRsp departmentEmployeeList = departmentEmployeeListResult.getData();
                                        if(StringUtils.isNotEmpty(departmentEmployeeList.getUserlist().get(0).getName()) && !departmentEmployeeList.getUserlist().get(0).getName().equals(departmentEmployeeList.getUserlist().get(0).getUserid())) {
                                            info.setUserStatus(0);
                                        } else {
                                            info.setUserStatus(1);
                                        }
                                    } else {
                                        info.setUserStatus(1);
                                    }
                                    break;
                                }
                            }
                            if(info.getDepartmentStatus() == null) {
                                for(String depId : tagEmployeeListRsp.getPartylist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(repAppId, corpId, depId);
                                    if(departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
                                        QyweixinDepartmentInfoRsp departmentInfo = departmentInfoResult.getData();
                                        if(StringUtils.isNotEmpty(departmentInfo.getDepartment().getName()) && !departmentInfo.getDepartment().getName().equals(departmentInfo.getDepartment().getId())) {
                                            info.setDepartmentStatus(0);
                                        } else {
                                            info.setDepartmentStatus(1);
                                        }
                                    } else {
                                        info.setDepartmentStatus(1);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        return new Result<>(info);
    }

    @Override
    public Result<AccountObjInfo> queryCrmAccountObjStatus(String corpId) {
        String queryFsObject = fsManager.queryFsObject(ConfigCenter.MASTER_EA, "field_fsj82__c", corpId, "AccountObj");
        if(StringUtils.isNotEmpty(queryFsObject)) {
            AccountObjInfo info = new AccountObjInfo();
            JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
            if(read.size()!=0){
                String name = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].name")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].name").toString() : null;
                String tenantId = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].tenant_id")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].tenant_id").toString() : null;
                String channel = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].channel")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].channel").toString() : null;
                String lifeStatus = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].life_status")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].life_status").toString() : null;
                info.setName(name);
                info.setChannel(channel);
                info.setLifeStatus(lifeStatus);
                info.setCorpId(corpId);
                return new Result<>(info);
            }
        }
        return new Result<>();
    }

    @Override
    public Result<SalesOrderObjInfo> queryCrmSalesOrderObjStatus(String orderId) {
        String queryFsObject = fsManager.queryFsObject(ConfigCenter.MASTER_EA, "_id", orderId, "SalesOrderObj");
        SalesOrderObjInfo info = new SalesOrderObjInfo();
        if(StringUtils.isNotEmpty(queryFsObject)) {
            JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
            if(read.size()!=0){
                String salesOrderName = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].name")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].name").toString() : null;
                String tenantId = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].UDSText4__c")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].UDSText4__c").toString() : null;
                String salesOrderId = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0]._id")) ? JSONPath.read(queryFsObject, "$.data.dataList[0]._id").toString() : null;
                String lifeStatus = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].life_status")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].life_status").toString() : null;
                String orderStatus = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].order_status")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].order_status").toString() : null;
                info.setSalesOrderName(salesOrderName);
                info.setLifeStatus(lifeStatus);
                info.setSalesOrderId(salesOrderId);
                info.setOrderStatus(orderStatus);
            }
            return new Result<>(info);
        }
        return new Result<>();
    }

    @Override
    public Result<Integer> getEnterpriseRunStatus(String ea) {
        GetEnterpriseRunStatusResult runStatus = fsManager.getEnterpriseRunStatus(ea);
        if(ObjectUtils.isEmpty(runStatus) || runStatus.getRunStatus() == null) {
            return new Result<Integer>().addError(ErrorRefer.QUERRY_EMPTY.getCode(), "该企业不存在",null);
        }
        return new Result<>(runStatus.getRunStatus());
    }

    @Override
    public Result<Void> unbind(String corpId, String fsEa, String appId) {
        if(StringUtils.isNotEmpty(corpId) && StringUtils.isNotEmpty(fsEa)) {
            OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, fsEa, corpId, appId);
            qyweixinAccountBindInnerService.deleteQYWXAccountBind(enterpriseBindEntity);
        }
        return new Result<>();
    }

//    @Override
//    public Result<Void> updateAllCorpBind() {
//        Thread thread = new Thread(this::updateAllCorpBind2);
//        thread.start();
//        return new Result<>();
//    }
//
//    private void updateAllCorpBind2() {
//        String traceId = UUID.randomUUID().toString();
//        TraceUtil.initTrace("updateAllCorpBind-" + traceId);
//        //数据较多，分页去修改会好点
//        int pageSize = 1000;
//        int pageNum = 1;
//        boolean isHasNextPage = Boolean.TRUE;
//        do {
//            List<QyweixinCorpBindBo> corpBindBos = qyweixinCorpBindDao.queryAllCorpBind(((pageNum++) - 1) * pageSize, pageSize);
//            if(CollectionUtils.isEmpty(corpBindBos) || corpBindBos.size() < pageSize) {
//                isHasNextPage = Boolean.FALSE;
//            }
//            if(CollectionUtils.isNotEmpty(corpBindBos)) {
//                for(QyweixinCorpBindBo bindBo : corpBindBos) {
//                    int count = qyweixinCorpBindDao.update(bindBo);
//                    log.info("ToolsServiceImpl.updateAllCorpBind2,count={}, id={}",count, bindBo.getId());
//                }
//            }
//        } while(isHasNextPage);
//    }

//    @Override
//    public Result<Void> updateOpenIds(List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos) {
//
//        updateOpenIds2(qyweixinIdToOpenidBos);
//        Thread thread = new Thread(() -> updateOpenIds2(qyweixinIdToOpenidBos));
//        thread.start();
//        return new Result<>();
//    }

//    public Result<Void> updateOpenIds2(List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos) {
//        //查询出id，直接删除，保留一个
//        for(QyweixinIdToOpenidBo bo : qyweixinIdToOpenidBos) {
//            if(StringUtils.isAnyEmpty(bo.getCorpId(), bo.getOpenid(), bo.getPlaintextId())) {
//                continue;
//            }
//            List<String> ids = qyweixinIdToOpenidManager.getByOpenidAndPlaintextId(bo.getCorpId(), bo.getOpenid(), bo.getPlaintextId());
//            log.info("ToolsServiceImpl.updateOpenIds2,ids={}", ids);
//            if(CollectionUtils.isEmpty(ids) || ids.size() <= 1) {
//                continue;
//            }
//            for(int i = 1; i < ids.size(); i++) {
//                int count = qyweixinIdToOpenidManager.deleteById(ids.get(i));
//                log.info("ToolsServiceImpl.updateOpenIds2,id={}, count={}", i, count);
//            }
//        }
//        return new Result<>();
//    }



//    @Override
//    public Result<Void> updateAllCorpBindToCopy(Integer copyDirection) {
//        Thread thread = new Thread(() -> this.updateAllCorpBindToCopy2(copyDirection));
//        thread.start();
//        return new Result<>();
//    }

    @Override
    public Result<Long> deleteUserInfo(String outEa, Boolean isDelete) {
        DeleteResult result = oaConnectorOutUserInfoMongoDao.deleteUserInfoByOutEa(ChannelEnum.qywx, outEa);
        log.info("deleteUserInfo,result={}",result);
        return new Result<>(result.getDeletedCount());
    }

//    private void updateAllCorpBindToCopy2(Integer copyDirection) {
//        String traceId = UUID.randomUUID().toString();
//        TraceUtil.initTrace("updateAllCorpBindToCopy2-" + traceId);
//        //数据较多，分页去修改会好点
//        int pageSize = 1000;
//        int pageNum = 1;
//        boolean isHasNextPage = Boolean.TRUE;
//        do {
//            log.info("ToolsServiceImpl.updateAllCorpBindToCopy2,copyDirection={}",copyDirection);
//            if(copyDirection == 0) {
//                //原始字段复制到复制字段
//                List<String> getPermanentCodes = qyweixinCorpBindDao.getPermanentCode(((pageNum++) - 1) * pageSize, pageSize);
//                if(CollectionUtils.isEmpty(getPermanentCodes) || getPermanentCodes.size() < pageSize) {
//                    isHasNextPage = Boolean.FALSE;
//                }
//                getPermanentCodes = getPermanentCodes.stream()
//                        .filter(StringUtils::isNotEmpty)
//                        .collect(Collectors.toList());
//                if(CollectionUtils.isNotEmpty(getPermanentCodes)) {
//                    for(String code : getPermanentCodes) {
//                        int count = qyweixinCorpBindDao.updateCopyPermanentCode(SecurityUtil.decryptStr(code), code);
//                        log.info("ToolsServiceImpl.updateAllCorpBindToCopy2,count={}",count);
//                    }
//                }
//            } else if(copyDirection == 1) {
//                //复制字段复制到原始字段
//                List<String> getCopyPermanentCodes = qyweixinCorpBindDao.getCopyPermanentCode(((pageNum++) - 1) * pageSize, pageSize);
//                if(CollectionUtils.isEmpty(getCopyPermanentCodes) || getCopyPermanentCodes.size() < pageSize) {
//                    isHasNextPage = Boolean.FALSE;
//                }
//                getCopyPermanentCodes = getCopyPermanentCodes.stream()
//                        .filter(StringUtils::isNotEmpty)
//                        .collect(Collectors.toList());
//                if(CollectionUtils.isNotEmpty(getCopyPermanentCodes)) {
//                    for(String code : getCopyPermanentCodes) {
//                        int count = qyweixinCorpBindDao.updatePermanentCode1(code, SecurityUtil.decryptStr(code));
//                        log.info("ToolsServiceImpl.updateAllCorpBindToCopy2,count={}",count);
//                    }
//                }
//            } else {
//                isHasNextPage = Boolean.FALSE;
//            }
//        } while(isHasNextPage);
//    }

    @Override
    public Result<String> queryFsEnterpriseOpen(String outEa) {
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        //明文的corpId获取到的企业信息
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }
        List<OuterOaEnterpriseBindEntity> enterpriseMappingList = enterpriseBindEntities.stream().filter(v -> v.getBindType() == BindTypeEnum.auto).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return new Result<>("该企业没有自动创建的纷享企业");
        }

        if(enterpriseMappingList.get(0).getBindStatus() == BindStatusEnum.create) {
            return new Result<>(String.format("该企业创建中，企业ea=%s，创建时间=%s", enterpriseMappingList.get(0).getFsEa(), new Date(enterpriseMappingList.get(0).getCreateTime())));
        }
        return new Result<>(String.format("该企业已创建成功，企业ea=%s，创建时间=%s", enterpriseMappingList.get(0).getFsEa(), new Date(enterpriseMappingList.get(0).getUpdateTime())));
    }

    @Override
    public Result<String> queryFsEmployeeOpen(String outEa, String outUserId) {
        if(StringUtils.isAnyEmpty(outEa, outUserId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).outEmpId(outUserId).build());

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>("该人员创建失败，请联系集成平台相关研发人员排查");
        }
        if(employeeBindEntities.get(0).getBindStatus() == BindStatusEnum.create) {
            return new Result<>(String.format("该人员创建中，纷享人员id=%s，创建时间=%s", employeeBindEntities.get(0).getFsEmpId(), new Date(employeeBindEntities.get(0).getCreateTime())));
        }
        return new Result<>(String.format("该人员已创建成功，纷享人员id=%s，创建时间=%s", employeeBindEntities.get(0).getFsEmpId(), new Date(employeeBindEntities.get(0).getUpdateTime())));
    }

    @Override
    public Result<String> queryEnterpriseBindType(String fsEa, String outEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>("该企业没有绑定的账号，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }

        if(enterpriseBindEntities.get(0).getBindType() == BindTypeEnum.auto) {
            return new Result<>("应用开通的纷享企业");
        }
        return new Result<>("反绑定的纷享企业");
    }

    @Override
    public Result<String> queryFsEmployeeStatus(String outEa, String outUserId) {
        if(StringUtils.isAnyEmpty(outEa, outUserId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).outEmpId(outUserId).build());

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>("该人员没有绑定关系，请联系集成平台相关研发人员排查");
        }
        String fsEa = employeeBindEntities.get(0).getFsEa();
        String userId = employeeBindEntities.get(0).getFsEmpId();
        Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(fsEa, 1000, Lists.newArrayList(Integer.parseInt(userId)));
        if(ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
            return new Result<>(String.format("该人员未创建，纷享人员id=%s", userId));
        }
        if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
            return new Result<>(String.format("该人员已被停用，纷享人员id=%s", userId));
        }
        return new Result<>(String.format("该人员状态正常，纷享人员id=%s", userId));
    }

//    @Override
//    public Result<Void> dealRepeatEmployees() {
//        Thread thread = new Thread(() -> this.dealRepeatEmployees2());
//        thread.start();
//        return new Result<>();
//    }
//
//    @Override
//    public Result<Void> queryRepeatEmployees() {
//        Thread thread = new Thread(() -> this.queryRepeatEmployees2());
//        thread.start();
//        return new Result<>();
//    }
//
//    public Result<Void> dealRepeatEmployees2() {
//        TraceUtil.initTrace("dealRepeatEmployees2" + System.currentTimeMillis());
//        Integer sum = 0;
//        try {
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployeesResult = qyweixinAccountBindService.queryRepeatEmployees();
//            if(!queryRepeatEmployeesResult.isSuccess() || CollectionUtils.isEmpty(queryRepeatEmployeesResult.getData())) {
//                log.info("ToolsServiceImpl.dealRepeatEmployees2,queryRepeatEmployeesResult={}",queryRepeatEmployeesResult);
//                return new Result<>();
//            }
//            Map<String, List<QyweixinEmployeeAccountModel>> groupedByOutEaMap =
//                    queryRepeatEmployeesResult.getData().stream().collect(Collectors.groupingBy(QyweixinEmployeeAccountModel::getOutEa));
//
//            List<String> dealMsg = new LinkedList<>();
//            for (Map.Entry<String, List<QyweixinEmployeeAccountModel>> entry : groupedByOutEaMap.entrySet()) {
//                try {
//                    String outEa = entry.getKey();
//                    log.info("ToolsServiceImpl.dealRepeatEmployees2,outEa={}",outEa);
//                    List<QyweixinEmployeeAccountModel> employeeMappings = entry.getValue();
//                    String fsEa = employeeMappings.get(0).getFsEa();
//                    Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
//                    dealMsg.add(String.format("执行，fsEa=%s,outEa=%s\n", fsEa, outEa));
//                    //企业过期/停用/删除/不存在，直接处理
//                    GetEnterpriseRunStatusArg statusArg = new GetEnterpriseRunStatusArg();
//                    statusArg.setEnterpriseAccount(fsEa);
//                    GetEnterpriseRunStatusResult enterpriseRunStatus = enterpriseEditionService.getEnterpriseRunStatus(statusArg);
//                    if(ObjectUtils.isEmpty(enterpriseRunStatus)
//                            || enterpriseRunStatus.getRunStatus() == null
//                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_INVALIDATE
//                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_STOP
//                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_DELETE) {
//                        log.info("ToolsServiceImpl.queryRepeatEmployees,del ea,outEa={},fsEa={},GetEnterpriseRunStatusArg={},", outEa, fsEa, statusArg);
//                        for (QyweixinEmployeeAccountModel model : employeeMappings) {
//                            String outUserId = model.getOutAccount();
//                            log.info("ToolsServiceImpl.dealRepeatEmployees2,del ea,outUserId={}", outUserId);
//                            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount("qywx", fsEa, ConfigCenter.crmAppId, outUserId);
//                            //随机删除一个,这个直接删除库就行
//                            for (int i = 1; i < accountResult.getData().size(); i++) {
//                                int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ fsEa +".1000", Lists.newArrayList(accountResult.getData().get(i).getFsAccount()), ConfigCenter.crmAppId, outEa);
//                                log.info("ToolsServiceImpl.dealRepeatEmployees2,del ea,accountBind={}", accountBind);
//                                if (accountBind > 0) {
//                                    //企信通知
//                                    String msg = String.format("企业过期/停用/删除/不存在，直接处理，已删除，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
//                                    dealMsg.add(msg);
//                                } else {
//                                    //企信通知
//                                    String msg = String.format("企业过期/停用/删除/不存在，直接处理，删除关系失败，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
//                                    dealMsg.add(msg);
//                                }
//                            }
//                        }
//                        continue;
//                    }
//
//                    com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectDescribe>> listAllObjectsResult = fsObjServiceProxy.listAllObjects(ei);
//                    List<String> apiNames = listAllObjectsResult.getData().stream()
//                            .map(ObjectDescribe::getApiName)
//                            .filter(apiName -> apiName != null && !apiName.isEmpty())
//                            .collect(Collectors.toList());
//
//                    for(QyweixinEmployeeAccountModel model : employeeMappings) {
//                        String outUserId = model.getOutAccount();
//                        log.info("ToolsServiceImpl.dealRepeatEmployees2,outUserId={}",outUserId);
//                        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount("qywx", fsEa, ConfigCenter.crmAppId, outUserId);
//                        if(outEa.length() < 32 || model.getOutAccount().length() < 32) {
//                            //随机删除一个,这个直接删除库就行
//                            for(int i = 1; i < accountResult.getData().size(); i++) {
//                                int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("1000", Lists.newArrayList(accountResult.getData().get(i).getFsAccount()), ConfigCenter.crmAppId,model.getOutEa());
//                                log.info("ToolsServiceImpl.dealRepeatEmployees2,old,accountBind={}",accountBind);
//                                if(accountBind > 0) {
//                                    //企信通知
//                                    String msg = String.format("已删除，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
//                                    dealMsg.add(msg);
//                                } else {
//                                    //企信通知
//                                    String msg = String.format("删除关系失败，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
//                                    dealMsg.add(msg);
//                                }
//                            }
//                            continue;
//                        }
//
//                        List<String> userIds = new LinkedList<>();
//                        for(QyweixinAccountEmployeeMapping mapping : accountResult.getData()) {
//                            boolean flag = Boolean.FALSE;
//                            List<String> empAccounts = Splitter.on(".").splitToList(mapping.getFsAccount());
//                            String userId = empAccounts.get(2);
//                            log.info("ToolsServiceImpl.dealRepeatEmployees2,userId={}",userId);
//                            for(String apiName : apiNames) {
//                                if(apiName.equals("PersonnelObj") || apiName.equals("DepartmentObj")) {
//                                    continue;
//                                }
//                                String queryFsObject = fsManager.queryFsObject2(ei, "owner", userId, apiName);
//                                if(StringUtils.isNotEmpty(queryFsObject)) {
//                                    JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
//                                    if(CollectionUtils.isNotEmpty(read)) {
//                                        if (read.size() != 0) {
//                                            log.info("ToolsServiceImpl.dealRepeatEmployees2,flag={}",flag);
//                                            flag = Boolean.TRUE;
//                                            userIds.add(userId);
//                                            break;
//                                        }
//                                    }
//                                }
//                            }
//                            if(flag == Boolean.FALSE) {
//                                if("1000".equals(userId)) {
//                                    //管理员不删
//                                    log.info("ToolsServiceImpl.dealRepeatEmployees2,admin,outEa={},outUserId={},userIds={}", outEa, outUserId, userId);
//                                    userIds.add(userId);
//                                    continue;
//                                }
//
//                                //删除
//                                int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ fsEa +".1000", Lists.newArrayList(mapping.getFsAccount()), ConfigCenter.crmAppId, outEa);
//                                log.info("ToolsServiceImpl.dealRepeatEmployees2,accountBind={}",accountBind);
//                                //停用
//                                com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
//                                        ei + "",
//                                        userId,
//                                        false,
//                                        null,
//                                        null);
//                                log.info("ToolsServiceImpl.dealRepeatEmployees2,stopResult={}",stopResult);
//                                //企信通知
//                                String msg = String.format("已删除，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, userId);
//                                dealMsg.add(msg);
//
//                                //修改姓名
//                                String updateUserId;
//                                if(CollectionUtils.isNotEmpty(userIds)) {
//                                    updateUserId = userIds.get(0);
//                                } else {
//                                    updateUserId = userId;
//                                }
//                                com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoListResult = fsEmployeeServiceProxy.batchGetEmployeeDto(ei, Lists.newArrayList(Integer.valueOf(updateUserId)));
//                                log.info("ToolsServiceImpl.dealRepeatEmployees2,employeeDtoListResult={}",employeeDtoListResult);
//                                if(CollectionUtils.isNotEmpty(employeeDtoListResult.getData())
//                                        && (employeeDtoListResult.getData().get(0).getFullName().startsWith("U-FSQYWX") || employeeDtoListResult.getData().get(0).getName().startsWith("U-FSQYWX"))) {
//                                    log.info("ToolsServiceImpl.dealRepeatEmployees2,update name,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
//                                    //修改
//                                    FsEmpArg fsEmpArg = FsEmpArg.builder()
//                                            .ei(ei + "")
//                                            .id(updateUserId)
//                                            .name("U-FSQYWX" + outUserId)
//                                            .fullName("U-FSQYWX" + outUserId)
//                                            .build();
//                                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.update(fsEmpArg,
//                                            null,
//                                            null);
//                                    log.info("ToolsServiceImpl.dealRepeatEmployees2,update name,outEa={},outUserId={},update result={}", outEa, outUserId, result);
//                                }
//                                break;
//                            } else {
//                                log.info("ToolsServiceImpl.dealRepeatEmployees2,need del,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
//                            }
//                        }
//                        if(userIds.size() > 1) {
//                            log.info("ToolsServiceImpl.dealRepeatEmployees2,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
//                            //企信通知
//                            SendTextNoticeArg arg = new SendTextNoticeArg();
//                            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//                            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
//                            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//                            arg.setMsgTitle("--刷库信息--");
//                            String msg = String.format("超过两条绑定关系有数据，fsEa=%s,ei=%s,outEa=%s,outUserId=%s,userIds=%s", fsEa, ei, outEa, outUserId, userIds);
//                            arg.setMsg(msg);
//                            notificationService.sendQYWXNotice(arg);
//                        }
//                    }
//                    sum +=1;
//                    if(sum == 30) {
//                        //企信通知
//                        SendTextNoticeArg arg = new SendTextNoticeArg();
//                        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//                        List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
//                        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//                        arg.setMsgTitle("--刷库信息--");
//                        arg.setMsg(dealMsg.toString());
//                        notificationService.sendQYWXNotice(arg);
//                        return new Result<>();
//                    }
//                } catch (Exception e) {
//                    log.info("ToolsServiceImpl.dealRepeatEmployees2,error,", e);
//                }
//            }
//            //企信通知
//            SendTextNoticeArg arg = new SendTextNoticeArg();
//            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
//            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//            arg.setMsgTitle("--刷库信息--");
//            arg.setMsg(dealMsg.toString());
//            notificationService.sendQYWXNotice(arg);
//        } catch (Exception e) {
//            log.info("ToolsServiceImpl.dealRepeatEmployees2,error1,", e);
//        }
//        return new Result<>();
//    }

//    public Result<Void> queryRepeatEmployees2() {
//        TraceUtil.initTrace("queryRepeatEmployees2" + System.currentTimeMillis());
//        try {
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployeesResult = qyweixinAccountBindService.queryRepeatEmployees();
//            if(!queryRepeatEmployeesResult.isSuccess() || CollectionUtils.isEmpty(queryRepeatEmployeesResult.getData())) {
//                log.info("ToolsServiceImpl.queryRepeatEmployees2,queryRepeatEmployeesResult={}",queryRepeatEmployeesResult);
//                return new Result<>();
//            }
//            Map<String, List<QyweixinEmployeeAccountModel>> groupedByOutEaMap =
//                    queryRepeatEmployeesResult.getData().stream().collect(Collectors.groupingBy(QyweixinEmployeeAccountModel::getOutEa));
//
//            for (Map.Entry<String, List<QyweixinEmployeeAccountModel>> entry : groupedByOutEaMap.entrySet()) {
//                try {
//                    String outEa = entry.getKey();
//                    log.info("ToolsServiceImpl.queryRepeatEmployees2,outEa={}",outEa);
//                    List<QyweixinEmployeeAccountModel> employeeMappings = entry.getValue();
//                    String fsEa = employeeMappings.get(0).getFsEa();
//                    Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
//
//                    if(outEa.length() < 32) {
//                        log.info("ToolsServiceImpl.queryRepeatEmployees,old ea,outEa={},fsEa={}",outEa, fsEa);
//                        continue;
//                    }
//
//                    //企业过期/停用/删除/不存在，直接处理
//                    GetEnterpriseRunStatusArg statusArg = new GetEnterpriseRunStatusArg();
//                    statusArg.setEnterpriseAccount(fsEa);
//                    GetEnterpriseRunStatusResult enterpriseRunStatus = enterpriseEditionService.getEnterpriseRunStatus(statusArg);
//                    if(ObjectUtils.isEmpty(enterpriseRunStatus)
//                            || enterpriseRunStatus.getRunStatus() == null
//                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_INVALIDATE
//                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_STOP
//                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_DELETE) {
//                        log.info("ToolsServiceImpl.queryRepeatEmployees,del ea,outEa={},fsEa={},GetEnterpriseRunStatusArg={},",outEa, fsEa, statusArg);
//                        continue;
//                    }
//
//
//                    com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectDescribe>> listAllObjectsResult = fsObjServiceProxy.listAllObjects(ei);
//                    List<String> apiNames = listAllObjectsResult.getData().stream()
//                            .map(ObjectDescribe::getApiName)
//                            .filter(apiName -> apiName != null && !apiName.isEmpty())
//                            .collect(Collectors.toList());
//
//                    for(QyweixinEmployeeAccountModel model : employeeMappings) {
//                        String outUserId = model.getOutAccount();
//                        log.info("ToolsServiceImpl.queryRepeatEmployees2,outUserId={}",outUserId);
//                        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount("qywx", fsEa, ConfigCenter.crmAppId, outUserId);
//                        if(model.getOutAccount().length() < 32) {
//                            log.info("ToolsServiceImpl.queryRepeatEmployees,old outAccount,outEa={},fsEa={},outUserId{},",outEa, fsEa, outUserId);
//                            continue;
//                        }
//
//                        if(accountResult.getData().size() > 2) {
//                            //人工处理
//                            log.info("ToolsServiceImpl.queryRepeatEmployees,old,outEa={},fsEa={},outUserId{},fsUserIds={},",outEa, fsEa, outUserId, accountResult.getData());
//                            continue;
//                        }
//
//                        List<String> userIds = new LinkedList<>();
//                        for(QyweixinAccountEmployeeMapping mapping : accountResult.getData()) {
//                            boolean flag = Boolean.FALSE;
//                            List<String> empAccounts = Splitter.on(".").splitToList(mapping.getFsAccount());
//                            String userId = empAccounts.get(2);
//                            log.info("ToolsServiceImpl.queryRepeatEmployees2,userId={}",userId);
//                            for(String apiName : apiNames) {
//                                if(apiName.equals("PersonnelObj") || apiName.equals("DepartmentObj")) {
//                                    continue;
//                                }
//                                String queryFsObject = fsManager.queryFsObject2(ei, "owner", userId, apiName);
//                                if(StringUtils.isNotEmpty(queryFsObject)) {
//                                    JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
//                                    if(CollectionUtils.isNotEmpty(read)) {
//                                        if (read.size() != 0) {
//                                            log.info("ToolsServiceImpl.queryRepeatEmployees2,apiName={}",apiName);
//                                            flag = Boolean.TRUE;
//                                            userIds.add(userId);
//                                            break;
//                                        }
//                                    }
//                                }
//                            }
//
//                            if(flag == Boolean.FALSE) {
//                                //无数据，删除第一条
//                                log.info("ToolsServiceImpl.queryRepeatEmployees2,need del,outEa={},outUserId={},userId={}", outEa, outUserId, userId);
//                                break;
//                            } else {
//                                log.info("ToolsServiceImpl.queryRepeatEmployees2,need save,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
//                            }
//                        }
//                        if(userIds.size() > 1) {
//                            log.info("ToolsServiceImpl.queryRepeatEmployees2,repeat account,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
//                            //企信通知
//                            SendTextNoticeArg arg = new SendTextNoticeArg();
//                            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//                            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
//                            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//                            arg.setMsgTitle("--刷库信息--");
//                            String msg = String.format("超过两条绑定关系有数据，fsEa=%s,ei=%s,outEa=%s,outUserId=%s,userIds=%s", fsEa, ei, outEa, outUserId, userIds);
//                            arg.setMsg(msg);
//                            notificationService.sendQYWXNotice(arg);
//                        }
//                    }
//                } catch (Exception e) {
//                    log.info("ToolsServiceImpl.queryRepeatEmployees2,error,", e);
//                }
//            }
//        } catch (Exception e) {
//            log.info("ToolsServiceImpl.queryRepeatEmployees2,error1,", e);
//        }
//        return new Result<>();
//    }

//    @Override
//    public Result<Void> stopEmployee(Integer ei, String userId, String outEa) {
//        //删除
//        int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ eieaConverter.enterpriseIdToAccount(ei) +".1000", Lists.newArrayList("E." + eieaConverter.enterpriseIdToAccount(ei) + "." + userId), ConfigCenter.crmAppId, outEa);
//        log.info("ToolsServiceImpl.stopEmployee,accountBind={}",accountBind);
//        //停用
//        com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
//                ei + "",
//                userId,
//                false,
//                null,
//                null);
//        log.info("ToolsServiceImpl.stopEmployee,stopResult={}",stopResult);
//        //企信通知
//        SendTextNoticeArg arg = new SendTextNoticeArg();
//        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//        List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
//        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//        arg.setMsgTitle("--刷库信息--");
//        String msg = String.format("已删除，ei=%s,userId=%s", ei, userId);
//        arg.setMsg(msg);
//        notificationService.sendQYWXNotice(arg);
//        return new Result<>();
//    }

//    @Override
//    public Result<Void> dealEmpData(Integer ei, String userId, String newUserId, String outEa) {
//        Thread thread = new Thread(() -> this.dealEmpData2(ei, userId, newUserId, outEa));
//        thread.start();
//        return new Result<>();
//    }
//
//    public Result<Void> dealEmpData2(Integer ei, String userId, String newUserId, String outEa) {
//        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
//        List<String> dealMsg = new LinkedList<>();
//        //删除老的人员的绑定关系
//        //删除
//        int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ fsEa +".1000", Lists.newArrayList("E."+ fsEa + "." + userId), ConfigCenter.crmAppId, outEa);
//        log.info("ToolsServiceImpl.dealRepeatEmployees2,accountBind={}",accountBind);
//        //停用
//        com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
//                ei + "",
//                userId,
//                false,
//                null,
//                null);
//        log.info("ToolsServiceImpl.dealRepeatEmployees2,stopResult={}",stopResult);
//        //企信通知
//        String msg = String.format("已删除，fsEa=%s,userId=%s，删除结果：accountBind=%s，stopResult=%s\n", fsEa, userId, accountBind, stopResult);
//        dealMsg.add(msg);
//        //对象处理
//        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectDescribe>> listAllObjectsResult = fsObjServiceProxy.listAllObjects(ei);
//        List<String> apiNames = listAllObjectsResult.getData().stream()
//                .map(ObjectDescribe::getApiName)
//                .filter(apiName -> apiName != null && !apiName.isEmpty())
//                .collect(Collectors.toList());
//        for(String apiName : apiNames) {
//            if (apiName.equals("PersonnelObj") || apiName.equals("DepartmentObj")) {
//                continue;
//            }
//            String queryFsObject = fsManager.queryFsObject2(ei, "owner", userId, apiName);
//            if(StringUtils.isNotEmpty(queryFsObject)) {
//                JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
//                if(CollectionUtils.isNotEmpty(read)) {
//                    if (read.size() != 0) {
//                        log.info("ToolsServiceImpl.queryRepeatEmployees2,apiName={}",apiName);
//                        for (int i = 0; i < read.size(); i++) {
//                            // 获取每个对象
//                            JSONObject item = read.getJSONObject(i);
//                            // 获取该对象的 "_id" 字段值
//                            String id = item.getString("_id");
//                            com.fxiaoke.crmrestapi.common.result.Result<ActionChangeOwnerResult> actionChangeOwnerResultResult = fsManager.changeOwner(ei, apiName, id, Integer.valueOf(newUserId));
//                            log.info("ToolsServiceImpl.queryRepeatEmployees2,actionChangeOwnerResultResult={}",actionChangeOwnerResultResult);
//                            try {
//                                Thread.sleep(200L);
//                            } catch (InterruptedException e) {
//                                e.printStackTrace();
//                            }
//                            if(!actionChangeOwnerResultResult.isSuccess()) {
//                                dealMsg.add(String.format("更换负责人失败，fsEa=%s,userId=%s,apiName=%s更换负责人结果：actionChangeOwnerResultResult=%s\n", fsEa, userId, apiName, actionChangeOwnerResultResult));
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        //企信通知
//        SendTextNoticeArg arg = new SendTextNoticeArg();
//        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//        List<String> receivers = new LinkedList<>(ConfigCenter.DEAL_EMP_DATA_MEMBERS);
//        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//        arg.setMsgTitle("--同步信息--");
//        arg.setMsg(dealMsg.toString());
//        notificationService.sendQYWXNotice(arg);
//        return new Result<>();
//    }

//    @Override
//    public Result<Integer> updateDeptBindStatus(String fsEa, String fsDeptId, Integer status, String appId) {
//        com.facishare.open.qywx.accountbind.result.Result<Integer> integerResult = qyweixinAccountBindService.batchUpdateFsDepBindStatus(fsEa, Lists.newArrayList(fsDeptId), status, appId);
//        return new Result<>(integerResult.getData());
//    }
//
//    @Override
//    public Result<Void> pushCorpBindData2Cloud(String domain) {
//        //迁移所有数据
//        Thread thread = new Thread(() -> pushCorpBindData2Cloud2(domain));
//        thread.start();
//        return new Result<>();
//    }
//
//    private void pushCorpBindData2Cloud2(String domain) {
//        String traceId = UUID.randomUUID().toString();
//        TraceUtil.initTrace("pushCorpBindData2Cloud-" + traceId);
//        //数据较多，分页
//        int pageSize = 1000;
//        int pageNum = 1;
//        boolean isHasNextPage = Boolean.TRUE;
//        Gson gson = new Gson();
//        do {
//            List<QyweixinCorpBindBo> corpBindBos = qyweixinCorpBindDao.queryAllCorpBind(((pageNum++) - 1) * pageSize, pageSize);
//            if(CollectionUtils.isEmpty(corpBindBos) || corpBindBos.size() < pageSize) {
//                isHasNextPage = Boolean.FALSE;
//            }
//            if(CollectionUtils.isNotEmpty(corpBindBos)) {
//                String json = gson.toJson(corpBindBos);
//                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "qyweixinCorpBindDao", json, domain);
//                try {
//                    Thread.sleep(200L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } while(isHasNextPage);
//        log.info("ToolsServiceImpl.pushCorpBindData2Cloud,end");
//    }
//
//    @Override
//    public Result<Void> pushEnterpriseData2Cloud(String fsEa, String domain) {
//        Gson gson = new Gson();
//
//        //查询企业绑定
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResultList(SourceTypeEnum.QYWX.getSourceType(),
//                fsEa);
//        if(enterpriseMappingResult.isSuccess() && ObjectUtils.isNotEmpty(enterpriseMappingResult.getData())) {
//            String json = gson.toJson(enterpriseMappingResult.getData());
//            eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "accountEnterpriseBindDao", json, domain);
//        }
//
//        //查询人员绑定
//        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
//                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, ConfigCenter.crmAppId, null);
//
//        if(CollectionUtils.isNotEmpty(qyweixinAccountEmployeeMappings)) {
//            String json = gson.toJson(qyweixinAccountEmployeeMappings);
//            eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "qyweixinAccountEmployeeBindDao", json, domain);
//        }
//        return new Result<>();
//    }
//
//    @Override
//    public Result<Integer> updateEnterpriseDomain(String fsEa, String domain) {
//        Result<Integer> result = qyweixinAccountBindInnerService.updateEnterpriseDomain(fsEa, domain);
//        log.info("ToolsServiceImpl,updateEnterpriseDomain={}",result);
//        return result;
//    }
//
//    @Override
//    public Result<Void> pushCorpInfoData2Cloud(String domain) {
//        //迁移所有数据
//        Thread thread = new Thread(() -> pushCorpInfoData2Cloud2(domain));
//        thread.start();
//        return new Result<>();
//    }

    private String getDownstreamOutEa(List<ListAppShareInfoRsp.CorpInfo> downstreamCorpList, String corpName) {
        for(ListAppShareInfoRsp.CorpInfo corpInfo : downstreamCorpList) {
            if(StringUtils.equalsIgnoreCase(corpInfo.getCorp_name(),corpName)) {
                return corpInfo.getCorpid();
            }
        }
        return null;
    }

    @Override
    public Result<String> batchCreateQywxConnector(BatchCreateQywxConnectorArg arg) {
        log.info("batchCreateQywxConnector,arg={}",arg);
        int failedCount = 0;
        int exceptionCount = 0;
        String upstreamCorpId = arg.getUpstreamCorpId();
        String upstreamAppId = arg.getUpstreamAppId();
        String upstreamAgentId = arg.getUpstreamAgentId();

        if(StringUtils.isEmpty(upstreamCorpId) || StringUtils.isEmpty(upstreamAppId) || StringUtils.isEmpty(upstreamAgentId)) {
            return new Result<>(TraceUtils.getTraceId()+":参数错误");
        }

        log.info("batchCreateQywxConnector,upstreamCorpId={},upstreamAppId={},upstreamAgentId={}",upstreamCorpId,upstreamAppId,upstreamAgentId);
        com.facishare.open.qywx.accountinner.result.Result<List<ListAppShareInfoRsp.CorpInfo>> listAppShareInfo = qyWeixinManager.listAppShareInfo(upstreamCorpId,
                upstreamAppId,
                upstreamAgentId,null);
        List<ListAppShareInfoRsp.CorpInfo> downstreamCorpList = listAppShareInfo.getData();
        log.info("batchImportEnterpriseBind,downstreamCorpList={}",downstreamCorpList);

        if(CollectionUtils.isNotEmpty(downstreamCorpList)) {
            for(BatchCreateQywxConnectorArg.EnterpriseMapping enterpriseMapping : arg.getBindList()) {
                String outEa = getDownstreamOutEa(downstreamCorpList,enterpriseMapping.getOutEn());
                String outEn = enterpriseMapping.getOutEn();
                String fsEa = enterpriseMapping.getFsEa();
                if(StringUtils.isEmpty(outEa)) {
                    failedCount ++;
                    log.info("batchImportEnterpriseBind,outEn not match,enterpriseMapping={}",enterpriseMapping);
                    continue;
                }
                try {
                    String dataCenterId = IdGenerator.get();
                    String tenantId = eieaConverter.enterpriseAccountToId(fsEa)+"";
                    String prefix = (System.currentTimeMillis()+"").substring(10);
                    String dataCenterName = arg.getDataCenterName();

                    QYWXConnectParam connectParam = new QYWXConnectParam();
                    connectParam.setFsEa(fsEa);
                    connectParam.setOutEa(outEa);
                    connectParam.setOutEn(outEn);
                    connectParam.setOutDepId("1");
                    connectParam.setDataCenterId(dataCenterId);
                    connectParam.setDataCenterName(dataCenterName);
                    connectParam.setBindType(BindTypeEnum.manual);


                    CreateConnectorArg createConnectorArg = new CreateConnectorArg();
                    createConnectorArg.setId(dataCenterId);
                    createConnectorArg.setChannel("CONNECTOR_QYWX");
                    createConnectorArg.setTenantId(tenantId);
                    createConnectorArg.setDataCenterName(dataCenterName);
                    createConnectorArg.setConnectParams(JSONObject.toJSONString(connectParam));

                    //创建企微连接器
                    Result<ErpConnectInfoEntity> result = erpdssManager.createConnector(createConnectorArg);
                    log.info("batchImportEnterpriseBind,createConnector,result={}",result);
                    if(!result.isSuccess()) {
                        failedCount ++;
                        log.info("batchImportEnterpriseBind,createConnector failed,enterpriseMapping={}",enterpriseMapping);
                        continue;
                    }

                    Result<Void> fsBindWithQywx = qyweixinGatewayInnerService.fsBindWithQywx(connectParam);
                    log.info("batchImportEnterpriseBind,fsBindWithQywx,result={}",fsBindWithQywx);
                    if(!fsBindWithQywx.isSuccess()) {
                        failedCount ++;
                        log.info("batchImportEnterpriseBind,fsBindWithQywx failed,enterpriseMapping={}",enterpriseMapping);
                        continue;
                    }
                } catch (Exception e) {
                    exceptionCount ++;
                    log.info("batchImportEnterpriseBind,exception,enterpriseMapping={}",enterpriseMapping,e);
                    continue;
                }
                log.info("batchImportEnterpriseBind,bind success,enterpriseMapping={}",enterpriseMapping);
            }
        } else {
            return new Result<>(TraceUtils.getTraceId()+":拉取企微下游共享应用企业列表为空");
        }
        log.info("batchImportEnterpriseBind,exception,failedCount={},exceptionCount={}",failedCount,exceptionCount);
        if(failedCount==0 && exceptionCount==0) {
            log.info("batchImportEnterpriseBind,all success");
        }
        Result<String> result = new Result<>();
        result.setData(failedCount+"");
        result.setErrorMsg(TraceUtils.getTraceId());
        return result;
    }

//    private void pushCorpInfoData2Cloud2(String domain) {
//        String traceId = UUID.randomUUID().toString();
//        TraceUtil.initTrace("pushCorpInfoData2Cloud-" + traceId);
//        //数据较多，分页
//        int pageSize = 1000;
//        int pageNum = 1;
//        boolean isHasNextPage = Boolean.TRUE;
//        Gson gson = new Gson();
//        do {
//            List<QyweixinCorpInfoBo> corpBindBos = qyweixinCorpInfoDao.queryAllCorpBind(((pageNum++) - 1) * pageSize, pageSize);
//            if(CollectionUtils.isEmpty(corpBindBos) || corpBindBos.size() < pageSize) {
//                isHasNextPage = Boolean.FALSE;
//            }
//            if(CollectionUtils.isNotEmpty(corpBindBos)) {
//                String json = gson.toJson(corpBindBos);
//                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "QyweixinCorpInfoBo", json, domain);
//                try {
//                    Thread.sleep(200L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } while(isHasNextPage);
//        log.info("ToolsServiceImpl.pushCorpInfoData2Cloud,end");
//    }

    @Override
    public Result<Void> migrateOrderInfo(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }

        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateOrderInfo-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());
            // 执行迁移任务
            migrateOrderInfo2(outerOaEnterpriseBindEntities);
        });

        thread.start();
        return new Result<>();
    }


    private void migrateOrderInfo2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        List<String> outEaList = outerOaEnterpriseBindEntities.stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .distinct()
                .collect(Collectors.toList());

        for(String outEa : outEaList) {
            log.info("Start migrating order info for outEa={}", outEa);

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                do {
                    // 分页查询mysql数据,增加企业过滤条件
                    List<QyweixinOrderInfoBo> orderInfos = qyweixinOrderInfoDao.queryOrderInfoByOutEa(outEa, ((pageNum++) - 1) * pageSize, pageSize);
                    if(CollectionUtils.isEmpty(orderInfos) || orderInfos.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(orderInfos)) {
                        for(QyweixinOrderInfoBo orderInfo : orderInfos) {
                            try {
                                // 检查是否已存在
                                List<OuterOaOrderInfoEntity> existingEntities = outerOaOrderInfoManager.getPaidOrders(
                                    ChannelEnum.qywx,
                                    orderInfo.getPaidCorpid(),
                                    null,
                                    orderInfo.getOrderId()
                                );

                                if(CollectionUtils.isNotEmpty(existingEntities)) {
                                    log.info("Order already exists, skip migration. orderId={}, outEa={}",
                                        orderInfo.getOrderId(), outEa);
                                    continue;
                                }

                                // 构建PG表实体
                                OuterOaOrderInfoEntity entity = new OuterOaOrderInfoEntity();
                                entity.setId(IdGenerator.get());
                                entity.setChannel(ChannelEnum.qywx);
                                entity.setOrderId(orderInfo.getOrderId());

                                // 订单类型转换
                                OuterOaOrderInfoTypeEnum orderType;
                                switch(orderInfo.getOrderType()) {
                                    case 0:
                                        orderType = OuterOaOrderInfoTypeEnum.buy; // 普通订单
                                        break;
                                    case 1:
                                        orderType = OuterOaOrderInfoTypeEnum.expand; // 扩容订单
                                        break;
                                    case 2:
                                        orderType = OuterOaOrderInfoTypeEnum.renew; // 续期
                                        break;
                                    case 3:
                                        orderType = OuterOaOrderInfoTypeEnum.upgrade; // 升级
                                        break;
                                    default:
                                        orderType = OuterOaOrderInfoTypeEnum.buy;
                                }
                                entity.setOrderType(orderType);

                                entity.setAppId(StringUtils.defaultIfEmpty(orderInfo.getAppId(), ConfigCenter.crmAppId));
                                entity.setPaidOutEa(orderInfo.getPaidCorpid());
                                if (orderInfo.getBeginTime() != null) {
                                    entity.setBeginTime(orderInfo.getBeginTime() * 1000L);
                                }

                                if (orderInfo.getEndTime() != null) {
                                    entity.setEndTime(orderInfo.getEndTime() * 1000L);
                                }

                                // 构建订单详情JSON
                                QyweixinOrderInfo orderInfoDetail = new QyweixinOrderInfo();
                                orderInfoDetail.setOrderStatus(orderInfo.getOrderStatus());
                                orderInfoDetail.setEditionId(orderInfo.getEditionId());
                                orderInfoDetail.setEditionName(orderInfo.getEditionName());
                                orderInfoDetail.setPrice(orderInfo.getPrice());
                                orderInfoDetail.setUserCount(orderInfo.getUserCount());
                                orderInfoDetail.setOrderPeriod(orderInfo.getOrderPeriod());
                                if (orderInfo.getOrderTime() != null) {
                                    orderInfoDetail.setOrderTime(orderInfo.getOrderTime() * 1000L);
                                }
                                if (orderInfo.getPaidTime() != null) {
                                    orderInfoDetail.setPaidTime(orderInfo.getPaidTime() * 1000L);
                                }
                                orderInfoDetail.setProcessingStatus(orderInfo.getProcessingStatus());
                                orderInfoDetail.setOperatorId(orderInfo.getOperatorId());
                                orderInfoDetail.setOrderFrom(orderInfo.getOrderFrom());
                                orderInfoDetail.setOperatorCorpId(orderInfo.getOperatorCorpid());
                                orderInfoDetail.setPricePlanType(PricePlanTypeEnum.per_seat_per_year);

                                entity.setOrderInfo(JSON.toJSONString(orderInfoDetail));
                                entity.setCreateTime(orderInfo.getGmtCreate().getTime());
                                entity.setUpdateTime(orderInfo.getGmtModified().getTime());

                                // 保存到PG
                                outerOaOrderInfoManager.insert(entity);
                                successCount++;

                                // 每处理100条暂停100ms,避免数据库压力过大
                                if(successCount % 100 == 0) {
                                    Thread.sleep(100);
                                    log.info("Migrated {} orders for outEa={}", successCount, outEa);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to migrate order info, orderId={}, outEa={}",
                                    orderInfo.getOrderId(), outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating orders for outEa={}, success={}, fail={}",
                    outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate orders for outEa={}", outEa, e);
            }
        }

        log.info("Order info migration completed for all enterprises. Total success={}, total fail={}",
            totalSuccess, totalFail);
    }

    @Override
    public Result<Void> migrateBusinessInfoBind(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }

        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateBusinessInfoBind-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateBusinessInfoBind2(outerOaEnterpriseBindEntities);
        });

        thread.start();
        return new Result<>();
    }


    private void migrateBusinessInfoBind2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 100; // 每批处理的数据量
        List<String> outEaList = outerOaEnterpriseBindEntities.stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .distinct()
                .collect(Collectors.toList());

        for(String outEa : outEaList) {
            log.info("Start migrating business info bind for outEa={}", outEa);

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                do {
                    // 分页查询mysql数据,增加企业过滤条件
                    List<QyweixinBusinessInfoBindBo> businessInfoBinds = qyweixinBusinessInfoBindDao.queryBusinessInfoBindByOutEa(outEa, ((pageNum++) - 1) * pageSize, pageSize);
                    if(CollectionUtils.isEmpty(businessInfoBinds) || businessInfoBinds.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(businessInfoBinds)) {
                        // 按batchSize分批处理数据
                        List<QyweixinBusinessInfoBindEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinBusinessInfoBindBo bindBo : businessInfoBinds) {
                            try {
                                // 检查是否已存在
//                                List<QyweixinBusinessInfoBindEntity> entities = qyweixinBusinessInfoBindManager.getEntities(
//                                    QyweixinBusinessInfoBindParams.builder()
//                                        .fsEa(bindBo.getFsEa())
//                                        .outEa(bindBo.getOutEa())
//                                        .appId(bindBo.getAppId())
//                                        .build()
//                                );
//
//                                if(CollectionUtils.isNotEmpty(entities)) {
//                                    log.info("Business info bind already exists, skip migration. outEa={}", bindBo.getOutEa());
//                                    continue;
//                                }

                                // 构建PG表实体
                                QyweixinBusinessInfoBindEntity entity = new QyweixinBusinessInfoBindEntity();
                                entity.setId(IdGenerator.get());
                                entity.setFsEa(bindBo.getFsEa());
                                entity.setOutEa(bindBo.getOutEa());
                                entity.setAppId(StringUtils.defaultIfEmpty(bindBo.getAppId(), ConfigCenter.crmAppId));
                                entity.setBusinessType(bindBo.getBusinessType());
                                entity.setStatus(bindBo.getStatus());
                                entity.setCreateTime(bindBo.getCreateTime().getTime());
                                entity.setUpdateTime(bindBo.getUpdateTime().getTime());

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {
                                    int inserted = qyweixinBusinessInfoBindManager.batchUpsertInfos(batchEntities);
                                    successCount += inserted;
                                    log.info("Batch inserted {} business info binds for outEa={}", inserted, outEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare business info bind, outEa={}", bindBo.getOutEa(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {
                                int inserted = qyweixinBusinessInfoBindManager.batchUpsertInfos(batchEntities);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} business info binds for outEa={}", inserted, outEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining business info binds for outEa={}", outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating business info binds for outEa={}, success={}, fail={}",
                        outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate business info binds for outEa={}", outEa, e);
            }
        }

        log.info("Business info bind migration completed for all enterprises. Total success={}, total fail={}",
                totalSuccess, totalFail);
    }

    public Result<Void> migrateQyweixinIdToOpenid(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateQyweixinIdToOpenid-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateQyweixinIdToOpenid2(outerOaEnterpriseBindEntities);
        });
        thread.start();
        return new Result<>();
    }


    private void migrateQyweixinIdToOpenid2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 100; // 每批处理的数据量
        List<String> outEaList = outerOaEnterpriseBindEntities.stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .distinct()
                .collect(Collectors.toList());

        for(String outEa : outEaList) {
            log.info("Start migrating qyweixin id to openid for outEa={}", outEa);

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                do {
                    // 分页查询mysql数据,增加企业过滤条件
                    List<QyweixinIdToOpenidBo> idToOpenids = qyweixinIdToOpenidDao.queryIdToOpenidByOutEa(outEa, ((pageNum++) - 1) * pageSize, pageSize);
                    if(CollectionUtils.isEmpty(idToOpenids) || idToOpenids.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(idToOpenids)) {
                        // 按batchSize分批处理数据
                        List<QyweixinIdToOpenidEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinIdToOpenidBo idToOpenid : idToOpenids) {
                            try {
                                // 构建PG表实体
                                QyweixinIdToOpenidEntity entity = new QyweixinIdToOpenidEntity();
                                entity.setId(IdGenerator.get());
                                entity.setOutEa(idToOpenid.getCorpId());
                                entity.setOpenid(idToOpenid.getOpenid());
                                entity.setPlaintextId(idToOpenid.getPlaintextId());
                                entity.setType(idToOpenid.getType());
                                entity.setCreateTime(idToOpenid.getCreateTime().getTime());
                                entity.setUpdateTime(idToOpenid.getUpdateTime().getTime());

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {
                                    int inserted = qyweixinIdToOpenidManager.batchUpsertInfos(batchEntities);
                                    successCount += inserted;
                                    log.info("Batch inserted {} id to openid mappings for outEa={}", inserted, outEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare id to openid mapping, corpId={}, openid={}, plaintextId={}",
                                    idToOpenid.getCorpId(), idToOpenid.getOpenid(), idToOpenid.getPlaintextId(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {
                                int inserted = qyweixinIdToOpenidManager.batchUpsertInfos(batchEntities);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} id to openid mappings for outEa={}", inserted, outEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining id to openid mappings for outEa={}", outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating id to openid mappings for outEa={}, success={}, fail={}",
                        outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate id to openid mappings for outEa={}", outEa, e);
            }
        }

        log.info("Id to openid mapping migration completed for all enterprises. Total success={}, total fail={}",
                totalSuccess, totalFail);
    }

    @Override
    public Result<Void> migrateFileInfo(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateFileInfo-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateFileInfo2(outerOaEnterpriseBindEntities);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateFileInfo2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 100; // 每批处理的数据量
        List<String> fsEaList = outerOaEnterpriseBindEntities.stream()
                .map(OuterOaEnterpriseBindEntity::getFsEa)
                .distinct()
                .collect(Collectors.toList());

        for(String fsEa : fsEaList) {
            log.info("Start migrating file info for fsEa={}", fsEa);

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                do {
                    // 分页查询mysql数据,增加企业过滤条件
                    List<QyweixinFileBo> fileInfos = qyweixinFileDao.queryFileInfoByOutEa(fsEa, ((pageNum++) - 1) * pageSize, pageSize);
                    if(CollectionUtils.isEmpty(fileInfos) || fileInfos.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(fileInfos)) {
                        // 按batchSize分批处理数据
                        List<QyweixinFileEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinFileBo fileBo : fileInfos) {
                            try {
                                // 构建PG表实体
                                QyweixinFileEntity entity = new QyweixinFileEntity();
                                entity.setId(IdGenerator.get());
                                entity.setFsEa(fileBo.getFsEa());
                                entity.setMediaId(fileBo.getMediaId());
                                entity.setFileSize(fileBo.getFileSize());
                                entity.setType(fileBo.getType());
                                entity.setNpath(fileBo.getNpath());
                                entity.setUrl(fileBo.getUrl());
                                entity.setCreateAt(fileBo.getCreateAt().getTime());
                                entity.setErrMsg(fileBo.getErrMsg());
                                entity.setCreateTime(fileBo.getCreateTime().getTime());
                                entity.setUpdateTime(fileBo.getUpdateTime().getTime());

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {
                                    int inserted = qyweixinFileManager.batchUpsertInfos(batchEntities);
                                    successCount += inserted;
                                    log.info("Batch inserted {} files for fsEa={}", inserted, fsEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare file info, fsEa={}, mediaId={}",
                                    fileBo.getFsEa(), fileBo.getMediaId(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {
                                int inserted = qyweixinFileManager.batchUpsertInfos(batchEntities);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} files for fsEa={}", inserted, fsEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining files for fsEa={}", fsEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating files for fsEa={}, success={}, fail={}",
                        fsEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate files for fsEa={}", fsEa, e);
            }
        }

        log.info("File info migration completed for all enterprises. Total success={}, total fail={}",
                totalSuccess, totalFail);
    }

    @Override
    public Result<Void> migrateExternalContacts(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateExternalContacts-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateExternalContacts2(outerOaEnterpriseBindEntities);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateExternalContacts2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 100; // 每批处理的数据量
        List<String> outEaList = outerOaEnterpriseBindEntities.stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .distinct()
                .collect(Collectors.toList());

        for(String outEa : outEaList) {
            log.info("Start migrating external contacts for outEa={}", outEa);

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                do {
                    // 分页查询MySQL数据,增加企业过滤条件
                    List<QyweixinExternalContactBo> externalContacts = qyweixinExternalContactDao.queryExternalContactByOutEa(outEa, ((pageNum++) - 1) * pageSize, pageSize);
                    if(CollectionUtils.isEmpty(externalContacts) || externalContacts.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(externalContacts)) {
                        // 按batchSize分批处理数据
                        List<OuterOaExternalContactsEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinExternalContactBo contactBo : externalContacts) {
                            try {
                                // 构建PG表实体
                                OuterOaExternalContactsEntity entity = new OuterOaExternalContactsEntity();
                                entity.setId(IdGenerator.get());
                                entity.setChannel(ChannelEnum.qywx);
                                entity.setOutEa(contactBo.getOutEa());
                                entity.setOutUserId(contactBo.getOutUserId());
                                entity.setExternalUserId(contactBo.getExternalUserId());
                                entity.setExternalName(contactBo.getExternalName());
                                entity.setAvatar(contactBo.getAvatar());

                                // 构建外部联系人详情JSON
                                entity.setExternalContactsInfo("{}");

                                // 设置时间戳
                                entity.setCreateTime(contactBo.getCreateTime() != null ? contactBo.getCreateTime().getTime() : System.currentTimeMillis());
                                entity.setUpdateTime(contactBo.getUpdateTime() != null ? contactBo.getUpdateTime().getTime() : System.currentTimeMillis());

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {
                                    // 使用 LinkedHashMap 保留插入顺序的同时去重
                                    Map<String, OuterOaExternalContactsEntity> uniqueMap = new LinkedHashMap<>();
                                    for (OuterOaExternalContactsEntity contact : batchEntities) {
                                        String key = contact.getChannel() + "|" + contact.getOutEa() + "|" +
                                                contact.getOutUserId() + "|" + contact.getExternalUserId();
                                        uniqueMap.put(key, contact);
                                    }

                                    // 将去重后的数据转为List
                                    List<OuterOaExternalContactsEntity> uniqueList = new ArrayList<>(uniqueMap.values());

                                    int inserted = outerOaExternalContactsManager.batchUpsertInfos(uniqueList);
                                    successCount += inserted;
                                    log.info("Batch inserted {} external contacts for outEa={}", inserted, outEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare external contact, outEa={}, outUserId={}, externalUserId={}",
                                        contactBo.getOutEa(), contactBo.getOutUserId(), contactBo.getExternalUserId(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {
                                // 使用 LinkedHashMap 保留插入顺序的同时去重
                                Map<String, OuterOaExternalContactsEntity> uniqueMap = new LinkedHashMap<>();
                                for (OuterOaExternalContactsEntity contact : batchEntities) {
                                    String key = contact.getChannel() + "|" + contact.getOutEa() + "|" +
                                            contact.getOutUserId() + "|" + contact.getExternalUserId();
                                    uniqueMap.put(key, contact);
                                }

                                // 将去重后的数据转为List
                                List<OuterOaExternalContactsEntity> uniqueList = new ArrayList<>(uniqueMap.values());

                                int inserted = outerOaExternalContactsManager.batchUpsertInfos(uniqueList);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} external contacts for outEa={}", inserted, outEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining external contacts for outEa={}", outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating external contacts for outEa={}, success={}, fail={}",
                        outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate external contacts for outEa={}", outEa, e);
            }
        }

        log.info("External contacts migration completed for all enterprises. Total success={}, total fail={}",
                totalSuccess, totalFail);
    }

    @Override
    public Result<Void> migrateAppBind(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateAppBind-" + traceId);
                        // 执行迁移任务
            migrateAppBind2(outEaList);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateAppBind2(List<String> outEaList) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 100; // 每批处理的数据量

        for(String outEa : outEaList) {
            log.info("Start migrating app bindings for outEa={}", outEa);

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            Map<String, QyweixinGetAuthInfoRsp> corpAuthInfoMap = new HashMap<>();
            try {
                do {
                    // 分页查询MySQL数据,增加企业过滤条件
                    List<QyweixinCorpBindBo> corpBinds = qyweixinCorpBindDao.queryCorpBindByOutEa(outEa, ((pageNum++) - 1) * pageSize, pageSize);
                    if(CollectionUtils.isEmpty(corpBinds) || corpBinds.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(corpBinds)) {
                        // 按batchSize分批处理数据
                        List<OuterOaAppInfoEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinCorpBindBo corpBindBo : corpBinds) {
                            try {
                                //迁移过就不用再次迁移
                                List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpBindBo.getCorpId()).appId(corpBindBo.getAppId()).build());
                                if(CollectionUtils.isNotEmpty(appInfoEntities)){
                                    continue;
                                }

                                // 构建PG表实体
                                OuterOaAppInfoEntity entity = new OuterOaAppInfoEntity();
                                entity.setId(IdGenerator.get());
                                entity.setChannel(ChannelEnum.qywx);
                                entity.setOutEa(corpBindBo.getCorpId());
                                entity.setAppId(corpBindBo.getAppId());
                                entity.setAppType(StringUtils.startsWithIgnoreCase(corpBindBo.getAppId(),"dk") ? OuterOaAppInfoTypeEnum.serviceRepDev : OuterOaAppInfoTypeEnum.isv);

                                // 构建应用信息JSON
                                QyweixinAppInfoParams appInfoParams = new QyweixinAppInfoParams();
                                QyweixinAppInfoParams.AuthAppInfo authAppInfo = new QyweixinAppInfoParams.AuthAppInfo();
                                authAppInfo.setAgentId(Integer.parseInt(corpBindBo.getAgentId()));
                                authAppInfo.setName(corpBindBo.getCorpName());
                                appInfoParams.setAuthAppInfo(authAppInfo);

                                // 设置永久授权码
                                appInfoParams.setPermanentCode(corpBindBo.getPermanentCode());

                                entity.setAppInfo(JSON.toJSONString(appInfoParams));
                                entity.setStatus(corpBindBo.getStatus() == 0 ? OuterOaAppInfoStatusEnum.normal : OuterOaAppInfoStatusEnum.stop); // 默认为正常状态

                                // 设置时间戳
                                entity.setCreateTime(corpBindBo.getGetGmtCreate() != null ?
                                    corpBindBo.getGetGmtCreate().getTime() : System.currentTimeMillis());
                                entity.setUpdateTime(corpBindBo.getGetGmtModified() != null ?
                                    corpBindBo.getGetGmtModified().getTime() : System.currentTimeMillis());

                                if (!corpAuthInfoMap.containsKey(corpBindBo.getCorpId() + "_" + corpBindBo.getAppId())) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> authInfoRspResult =
                                            qyWeixinManager.getCorpInfo2(corpBindBo.getCorpId(), corpBindBo.getAppId(), corpBindBo.getPermanentCode());
                                    if (authInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(authInfoRspResult.getData())) {
                                        corpAuthInfoMap.put(corpBindBo.getCorpId() + "_" + corpBindBo.getAppId(), authInfoRspResult.getData());
                                    }
                                }
                                if (corpAuthInfoMap.containsKey(corpBindBo.getCorpId() + "_" + corpBindBo.getAppId())) {
                                    //更新应用信息
                                    QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp = new QyweixinGetPermenantCodeRsp();
                                    BeanUtils.copyProperties(corpAuthInfoMap.get(corpBindBo.getCorpId() + "_" + corpBindBo.getAppId()), qyweixinGetPermenantCodeRsp);
                                    saveCorpInfoTask(qyweixinGetPermenantCodeRsp, entity);
                                }

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {
                                    int inserted = outerOaAppInfoManager.batchUpsertInfos(batchEntities);
                                    successCount += inserted;
                                    log.info("Batch inserted {} app bindings for outEa={}", inserted, outEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare app binding, corpId={}, appId={}",
                                    corpBindBo.getCorpId(), corpBindBo.getAppId(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {
                                int inserted = outerOaAppInfoManager.batchUpsertInfos(batchEntities);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} app bindings for outEa={}", inserted, outEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining app bindings for outEa={}", outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating app bindings for outEa={}, success={}, fail={}",
                    outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate app bindings for outEa={}", outEa, e);
            }
        }

        log.info("App binding migration completed for all enterprises. Total success={}, total fail={}",
            totalSuccess, totalFail);
    }

    public void saveCorpInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, OuterOaAppInfoEntity entity) {
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(entity.getAppInfo(), QyweixinAppInfoParams.class);
        if (ObjectUtils.isEmpty(qyweixinAppInfoParams)) {
            qyweixinAppInfoParams = new QyweixinAppInfoParams();
        }

        // 设置 permanentCode
        if (org.apache.commons.lang.StringUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getPermanent_code())) {
            qyweixinAppInfoParams.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
        }

        // 设置 authCorpInfo
        if (ObjectUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_corp_info())) {
            QyweixinAuthCorpInfoRsp authCorpInfoRsp = qyweixinGetPermenantCodeRsp.getAuth_corp_info();
            QyweixinAppInfoParams.AuthCorpInfo authCorpInfo = new QyweixinAppInfoParams.AuthCorpInfo();
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_name())) {
                authCorpInfo.setCorpName(authCorpInfoRsp.getCorp_name());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_type())) {
                authCorpInfo.setCorpType(authCorpInfoRsp.getCorp_type());
            }
            if (ObjectUtils.isNotEmpty(authCorpInfoRsp.getSubject_type())) {
                authCorpInfo.setSubjectType(authCorpInfoRsp.getSubject_type());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_square_logo_url())) {
                authCorpInfo.setCorpSquareLogoUrl(authCorpInfoRsp.getCorp_square_logo_url());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_full_name())) {
                authCorpInfo.setCorpFullName(authCorpInfoRsp.getCorp_full_name());
            }
            qyweixinAppInfoParams.setAuthCorpInfo(authCorpInfo);
        }

        // 设置 authAppInfo
        if (ObjectUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_info())
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent())) {
            QyweixinAgentRsp agentRsp = qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0);
            QyweixinAppInfoParams.AuthAppInfo authAppInfo = new QyweixinAppInfoParams.AuthAppInfo();
            if (org.apache.commons.lang.StringUtils.isNotEmpty(agentRsp.getName())) {
                authAppInfo.setName(agentRsp.getName());
            }
            authAppInfo.setAgentId(agentRsp.getAgentid());
            if (org.apache.commons.lang.StringUtils.isNotEmpty(agentRsp.getRound_logo_url())) {
                authAppInfo.setRoundLogoUrl(agentRsp.getRound_logo_url());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(agentRsp.getSquare_logo_url())) {
                authAppInfo.setSquareLogoUrl(agentRsp.getSquare_logo_url());
            }
            if (ObjectUtils.isNotEmpty(agentRsp.getAuth_mode())) {
                authAppInfo.setAuthMode(agentRsp.getAuth_mode());
            }
            if (ObjectUtils.isNotEmpty(agentRsp.getShared_from())) {
                QyweixinAppInfoParams.AuthAppInfo.AgentSharedFrom sharedFrom = new QyweixinAppInfoParams.AuthAppInfo.AgentSharedFrom();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(agentRsp.getShared_from().getCorpid())) {
                    sharedFrom.setCorpId(agentRsp.getShared_from().getCorpid());
                }
                if (ObjectUtils.isNotEmpty(agentRsp.getShared_from().getShare_type())) {
                    sharedFrom.setShareType(agentRsp.getShared_from().getShare_type());
                }
                authAppInfo.setSharedFrom(sharedFrom);
            }
            qyweixinAppInfoParams.setAuthAppInfo(authAppInfo);
        }

        // 设置 authAppUserInfo
        if (ObjectUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_user_info())) {
            QyweixinAuthUserInfoRsp authUserInfoRsp = qyweixinGetPermenantCodeRsp.getAuth_user_info();
            QyweixinAppInfoParams.AuthAppUserInfo authAppUserInfo = new QyweixinAppInfoParams.AuthAppUserInfo();
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authUserInfoRsp.getName())) {
                authAppUserInfo.setName(authUserInfoRsp.getName());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authUserInfoRsp.getAvatar())) {
                authAppUserInfo.setAvatar(authUserInfoRsp.getAvatar());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(authUserInfoRsp.getUserid())) {
                authAppUserInfo.setUserId(authUserInfoRsp.getUserid());
            }
            qyweixinAppInfoParams.setAuthAppUserInfo(authAppUserInfo);
        }
        entity.setAppInfo(JSON.toJSONString(qyweixinAppInfoParams));
    }

    @Override
    public Result<Void> migrateEmpBind(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateEmpBind-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateEmpBind2(outerOaEnterpriseBindEntities);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateEmpBind2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 500; // 每批处理的数据量

        for(OuterOaEnterpriseBindEntity enterpriseBindEntity : outerOaEnterpriseBindEntities) {
            log.info("Start migrating employee bindings for enterpriseBindEntity={}", enterpriseBindEntity);
            String outEa = enterpriseBindEntity.getOutEa();
            String fsEa = enterpriseBindEntity.getFsEa();
            String appId = enterpriseBindEntity.getAppId();
            String dcId = enterpriseBindEntity.getId();
            // 分页查询,每次处理10000条
            int pageSize = 10000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                // 使用 LinkedHashMap 保留插入顺序的同时去重
                Set<String> uniqueOutIds = new LinkedHashSet<>();
                Set<String> uniqueFsIds = new LinkedHashSet<>();
                do {
                    // 分页查询MySQL数据,增加企业过滤条件
                    List<QyweixinAccountEmployeeMapping> employeeMappings = accountEmployeeBindDao.selectByPage(fsEa, outEa, (pageNum - 1) * pageSize, pageSize);
                    pageNum++;

                    if(CollectionUtils.isEmpty(employeeMappings) || employeeMappings.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(employeeMappings)) {
                        // 按batchSize分批处理数据
                        List<OuterOaEmployeeBindEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
                            try {
                                // 构建PG表实体
                                List<String> accountList = Splitter.on(".").splitToList(mapping.getFsAccount());
                                OuterOaEmployeeBindEntity entity = new OuterOaEmployeeBindEntity();
                                entity.setId(IdGenerator.get());
                                entity.setChannel(ChannelEnum.qywx);
                                entity.setOutEa(mapping.getOutEa());
                                entity.setDcId(dcId);
                                entity.setAppId(appId);
                                entity.setOutEmpId(mapping.getOutAccount());
                                entity.setFsEa(accountList.get(1));
                                entity.setFsEmpId(accountList.get(2));
                                entity.setBindStatus(mapping.getStatus() == 0 ? BindStatusEnum.normal : (mapping.getStatus() == 1 ? BindStatusEnum.stop : BindStatusEnum.create));

                                // 设置时间戳
                                entity.setCreateTime(mapping.getGmtCreate() != null ?
                                    mapping.getGmtCreate().getTime() : System.currentTimeMillis());
                                entity.setUpdateTime(mapping.getGmtModified() != null ?
                                    mapping.getGmtModified().getTime() : System.currentTimeMillis());

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {

                                    Map<String, OuterOaEmployeeBindEntity> uniqueMap = new LinkedHashMap<>();
                                    for (OuterOaEmployeeBindEntity contact : batchEntities) {
                                        String key = contact.getChannel() + "|" + contact.getOutEa() + "|" +
                                                contact.getOutEmpId();
                                        uniqueMap.put(key, contact);
                                        if (!uniqueOutIds.contains(key)) {
                                            uniqueOutIds.add(key);
                                        } else {
                                            log.error("Duplicate uniqueOutIds employee binding found, outEa={}, contact={}",
                                                    outEa, contact);
                                        }
                                    }

                                    // 将去重后的数据转为List
                                    List<OuterOaEmployeeBindEntity> uniqueList = new ArrayList<>(uniqueMap.values());

                                    Map<String, OuterOaEmployeeBindEntity> uniqueMap2 = new LinkedHashMap<>();
                                    for (OuterOaEmployeeBindEntity contact : uniqueList) {
                                        String key = contact.getChannel() + "|" + contact.getOutEa() + "|" +
                                                contact.getFsEmpId();
                                        uniqueMap2.put(key, contact);
                                        if (!uniqueFsIds.contains(key)) {
                                            uniqueFsIds.add(key);
                                        } else {
                                            log.error("Duplicate uniqueFsIds employee binding found, outEa={}, contact={}",
                                                    outEa, contact);
                                        }
                                    }

                                    // 将去重后的数据转为List
                                    List<OuterOaEmployeeBindEntity> uniqueList2 = new ArrayList<>(uniqueMap2.values());

                                    int inserted = outerOaEmployeeBindManager.batchUpsert(uniqueList2);
                                    successCount += inserted;
                                    log.info("Batch inserted {} employee bindings for outEa={}", inserted, outEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare employee binding, outEa={}, outUserId={}, fsUserId={}",
                                        mapping.getOutEa(), mapping.getOutAccount(), mapping.getFsAccount(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {

                                // 使用 LinkedHashMap 保留插入顺序的同时去重
                                Map<String, OuterOaEmployeeBindEntity> uniqueMap = new LinkedHashMap<>();
                                for (OuterOaEmployeeBindEntity contact : batchEntities) {
                                    String key = contact.getChannel() + "|" + contact.getOutEa() + "|" +
                                            contact.getOutEmpId();
                                    uniqueMap.put(key, contact);
                                    if (!uniqueOutIds.contains(key)) {
                                        uniqueOutIds.add(key);
                                    } else {
                                        log.info("Duplicate uniqueOutIds employee binding found, outEa={}, outUserId={}, fsUserId={}",
                                                outEa, contact.getOutEmpId(), contact.getFsEmpId());
                                    }
                                }

                                // 将去重后的数据转为List
                                List<OuterOaEmployeeBindEntity> uniqueList = new ArrayList<>(uniqueMap.values());

                                Map<String, OuterOaEmployeeBindEntity> uniqueMap2 = new LinkedHashMap<>();
                                for (OuterOaEmployeeBindEntity contact : uniqueList) {
                                    String key = contact.getChannel() + "|" + contact.getOutEa() + "|" +
                                            contact.getFsEmpId();
                                    uniqueMap2.put(key, contact);
                                    if (!uniqueFsIds.contains(key)) {
                                        uniqueFsIds.add(key);
                                    } else {
                                        log.info("Duplicate uniqueFsIds employee binding found, outEa={}, outUserId={}, fsUserId={}",
                                                outEa, contact.getOutEmpId(), contact.getFsEmpId());
                                    }
                                }

                                // 将去重后的数据转为List
                                List<OuterOaEmployeeBindEntity> uniqueList2 = new ArrayList<>(uniqueMap2.values());

                                int inserted = outerOaEmployeeBindManager.batchUpsert(uniqueList2);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} employee bindings for outEa={}", inserted, outEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining employee bindings for outEa={}", outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating employee bindings for outEa={}, success={}, fail={}",
                        outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate employee bindings for outEa={}", outEa, e);
            }
        }

        log.info("Employee bindings migration completed for all enterprises. Total success={}, total fail={}",
                totalSuccess, totalFail);
    }

    @Override
    public Result<Void> migrateDeptBind(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateDeptBind-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateDeptBind2(outerOaEnterpriseBindEntities);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateDeptBind2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        int totalSuccess = 0;
        int totalFail = 0;
        int batchSize = 100; // 每批处理的数据量

        for(OuterOaEnterpriseBindEntity enterpriseBindEntity : outerOaEnterpriseBindEntities) {
            log.info("Start migrating department bindings for enterpriseBindEntity={}", enterpriseBindEntity);
            String outEa = enterpriseBindEntity.getOutEa();
            String fsEa = enterpriseBindEntity.getFsEa();
            String appId = enterpriseBindEntity.getAppId();
            String dcId = enterpriseBindEntity.getId();

            // 分页查询,每次处理1000条
            int pageSize = 1000;
            int pageNum = 1;
            boolean hasNextPage = true;
            int successCount = 0;
            int failCount = 0;

            try {
                do {
                    // 分页查询MySQL数据,增加企业过滤条件
                    List<QyweixinAccountDepartmentMapping> departmentMappings = accountDepartmentBindDao.selectByPage(fsEa, outEa, (pageNum - 1) * pageSize, pageSize);
                    pageNum++;

                    if(CollectionUtils.isEmpty(departmentMappings) || departmentMappings.size() < pageSize) {
                        hasNextPage = false;
                    }

                    if(CollectionUtils.isNotEmpty(departmentMappings)) {
                        // 按batchSize分批处理数据
                        List<OuterOaDepartmentBindEntity> batchEntities = new ArrayList<>(batchSize);

                        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
                            try {
                                // 构建PG表实体
                                OuterOaDepartmentBindEntity entity = new OuterOaDepartmentBindEntity();
                                entity.setId(IdGenerator.get());
                                entity.setChannel(ChannelEnum.qywx);
                                entity.setOutEa(mapping.getOutEa());
                                entity.setDcId(dcId);
                                entity.setAppId(appId);
                                entity.setOutDepId(mapping.getOutDepartmentId());
                                entity.setFsEa(mapping.getFsEa());
                                entity.setFsDepId(mapping.getFsDepartmentId().toString());
                                entity.setBindStatus(mapping.getStatus() == 0 ? BindStatusEnum.normal :
                                                   (mapping.getStatus() == 1 ? BindStatusEnum.stop : BindStatusEnum.create));

                                // 设置时间戳
                                entity.setCreateTime(mapping.getGmtCreate() != null ?
                                    mapping.getGmtCreate().getTime() : System.currentTimeMillis());
                                entity.setUpdateTime(mapping.getGmtModified() != null ?
                                    mapping.getGmtModified().getTime() : System.currentTimeMillis());

                                batchEntities.add(entity);

                                // 当达到批处理大小时执行批量插入
                                if(batchEntities.size() >= batchSize) {

                                    int inserted = outerOaDepartmentBindManager.batchUpsert(batchEntities);
                                    successCount += inserted;
                                    log.info("Batch inserted {} department bindings for outEa={}", inserted, outEa);
                                    batchEntities.clear();

                                    // 每批处理后暂停100ms
                                    Thread.sleep(100);
                                }

                            } catch(Exception e) {
                                failCount++;
                                log.error("Failed to prepare department binding, outEa={}, outDeptId={}, fsDeptId={}",
                                        mapping.getOutEa(), mapping.getOutDepartmentId(), mapping.getFsDepartmentId(), e);
                            }
                        }

                        // 处理剩余的数据
                        if(!batchEntities.isEmpty()) {
                            try {
                                int inserted = outerOaDepartmentBindManager.batchUpsert(batchEntities);
                                successCount += inserted;
                                log.info("Batch inserted remaining {} department bindings for outEa={}", inserted, outEa);
                            } catch(Exception e) {
                                failCount += batchEntities.size();
                                log.error("Failed to batch insert remaining department bindings for outEa={}", outEa, e);
                            }
                        }
                    }

                } while(hasNextPage);

                totalSuccess += successCount;
                totalFail += failCount;

                log.info("Completed migrating department bindings for outEa={}, success={}, fail={}",
                        outEa, successCount, failCount);

            } catch(Exception e) {
                log.error("Failed to migrate department bindings for outEa={}", outEa, e);
            }
        }

        log.info("Department bindings migration completed for all enterprises. Total success={}, total fail={}",
                totalSuccess, totalFail);
    }

    @Override
    public Result<Void> migrateConfigInfo(List<String> outEaList) {
        if (CollectionUtils.isEmpty(outEaList)) {
            return new Result<>();
        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateConfigInfo-" + traceId);
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outEaList.stream()
                    .flatMap(outEa -> {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
                        return enterpriseBindEntities.stream();
                    })
                    .collect(Collectors.toList());            // 执行迁移任务
            migrateConfigInfo2(outerOaEnterpriseBindEntities);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateConfigInfo2(List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities) {
        //没什么数据
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : outerOaEnterpriseBindEntities) {
            String outEa = enterpriseBindEntity.getOutEa();
            String fsEa = enterpriseBindEntity.getFsEa();
            String appId = enterpriseBindEntity.getAppId();
            String dcId = enterpriseBindEntity.getId();

            try {
                List<MessageGeneratingPo> messageGeneratingPos = messageGeneratingDao.querySettingByOutEa(outEa);
                if (CollectionUtils.isEmpty(messageGeneratingPos)) {
                    continue;
                }
                messageGeneratingPos = messageGeneratingPos.stream()
                        .sorted(Comparator.comparing(MessageGeneratingPo::getVersion, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());

                //合并同同类项
                OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.queryConnectorConfigInfoByDcId(dcId, OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG);
                if (ObjectUtils.isNotEmpty(configInfoEntity)) {
                    continue;
                }
                configInfoEntity = new OuterOaConfigInfoEntity();
                configInfoEntity.setId(IdGenerator.get());
                configInfoEntity.setChannel(ChannelEnum.qywx);
                configInfoEntity.setDcId(dcId);
                configInfoEntity.setFsEa(fsEa);
                configInfoEntity.setOutEa(outEa);
                configInfoEntity.setAppId(appId);
                configInfoEntity.setType(OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG);
                ConversationArchiveInfo conversationArchiveInfo = new ConversationArchiveInfo();
                conversationArchiveInfo.setIsOpen(Boolean.TRUE);
                conversationArchiveInfo.setSeq(0);
                //不做加密
                conversationArchiveInfo.setSecret(messageGeneratingPos.get(0).getSecret());
                MessageStorageArg messageStorageArg = JSON.parseObject(messageGeneratingPos.get(0).getStorageLocation(), MessageStorageArg.class);
                conversationArchiveInfo.setStorageLocation(messageStorageArg);
                conversationArchiveInfo.setNextCursor(messageStorageArg.getNextCursor());

                List<ConversationArchiveInfo.ConversationConfig> conversationConfigs = new ArrayList<>();
                for (MessageGeneratingPo messageGeneratingPo : messageGeneratingPos) {
                    ConversationArchiveInfo.ConversationConfig conversationConfig = new ConversationArchiveInfo.ConversationConfig();
                    conversationConfig.setVersion(messageGeneratingPo.getVersion());
                    conversationConfig.setPublicKey(messageGeneratingPo.getPublicKey());
                    conversationConfig.setPrivateKey(messageGeneratingPo.getPrivateKey());
                    conversationConfigs.add(conversationConfig);
                }
                conversationArchiveInfo.setConversationConfigs(conversationConfigs);

                configInfoEntity.setConfigInfo(JSON.toJSONString(conversationArchiveInfo));
                configInfoEntity.setCreateTime(System.currentTimeMillis());
                configInfoEntity.setUpdateTime(System.currentTimeMillis());

                Integer count = outerOaConfigInfoManager.insert(configInfoEntity);
                log.info("setCallback insert count:{}",count);
            } catch (Exception e) {
                log.info("setCallback insert error:{}", e.getMessage());
            }
        }
    }

    @Override
    public Result<Void> migrateEnterpriseBind(List<String> outEaList, Timestamp startTime, Timestamp endTime, String finalAppId) {
//        if (CollectionUtils.isEmpty(outEaList)) {
//            return new Result<>();
//        }
        // 生成唯一的 traceId
        String traceId = UUID.randomUUID().toString();

        // 启动异步线程
        Thread thread = new Thread(() -> {
            // 初始化 traceId
            TraceUtils.initTrace("migrateEnterpriseBind-" + traceId);
                 // 执行迁移任务
            migrateEnterpriseBind2(outEaList, startTime, endTime, finalAppId);
        });
        thread.start();
        return new Result<>();
    }

    private void migrateEnterpriseBind2(List<String> outEaList, Timestamp startTime, Timestamp endTime, String finalAppId) {
        log.info("migrateEnterpriseBind2 start, outEaList:{}, startTime:{}, endTime:{}", outEaList, startTime, endTime);
        try {
            List<QyweixinAccountEnterpriseMapping> qyweixinAccountEnterpriseMappings = null;
            if (CollectionUtils.isNotEmpty(outEaList)) {
                qyweixinAccountEnterpriseMappings = qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEaList(outEaList);
            } else {
                qyweixinAccountEnterpriseMappings = qyweixinAccountEnterpriseBindDao.queryEaMappingByTimeRange(startTime, endTime);
            }

            for (QyweixinAccountEnterpriseMapping mapping : qyweixinAccountEnterpriseMappings) {
                log.info("migrateEnterpriseBind2 start, mapping:{}", mapping);
                String outEa = mapping.getOutEa();
                String fsEa = mapping.getFsEa();

                OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = new OuterOaEnterpriseBindEntity();
                outerOaEnterpriseBindEntity.setId(IdGenerator.get());
                outerOaEnterpriseBindEntity.setChannel(ChannelEnum.qywx);
                outerOaEnterpriseBindEntity.setOutEa(outEa);
                outerOaEnterpriseBindEntity.setFsEa(fsEa);
                outerOaEnterpriseBindEntity.setBindType(mapping.getBindType() == 1 ? BindTypeEnum.manual : BindTypeEnum.auto);
                outerOaEnterpriseBindEntity.setBindStatus(mapping.getStatus() == 0 ? BindStatusEnum.normal : (mapping.getStatus() == 1 ? BindStatusEnum.stop : BindStatusEnum.create));
                QywxConnectorVo qywxConnectorVo = new QywxConnectorVo();
                qywxConnectorVo.setDataCenterId(outerOaEnterpriseBindEntity.getId());
                qywxConnectorVo.setConnectorName("企业微信"); //ignoreI18n
                qywxConnectorVo.setDataCenterName("企业微信"); //ignoreI18n
                qywxConnectorVo.setChannel(ChannelEnum.qywx);
                //待办提醒默认为开启
                qywxConnectorVo.setAlertConfig(true);
                qywxConnectorVo.setAlertTypes(Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION));
                QyweixinCorpInfoBo qyweixinCorpInfoBo = qyweixinCorpInfoDao.queryCorpBindByCorpId(outEa);
                if (ObjectUtils.isNotEmpty(qyweixinCorpInfoBo)) {
                    qywxConnectorVo.setQywxEnterpriseName(qyweixinCorpInfoBo.getCorpName());
                }
                qywxConnectorVo.setQywxCorpId(mapping.getOutEa());
                qywxConnectorVo.setQywxDepartmentId(mapping.getDepId());
                qywxConnectorVo.setDomain(mapping.getDomain());
                EnterpriseExtendModel extendModel = JSON.parseObject(mapping.getExtend(), EnterpriseExtendModel.class);
                if (ObjectUtils.isNotEmpty(extendModel)) {
                    qywxConnectorVo.setIsFirstLand(extendModel.getIsFirstLand());
                    qywxConnectorVo.setIsRetainInformation(extendModel.getIsRetainInformation());
                }

                //appId从info表里找
                List<QyweixinCorpBindBo> corpBindBoList = qyweixinCorpBindDao.getByOutEa(outEa);
                //没有，默认为crmappId
                if (CollectionUtils.isEmpty(corpBindBoList)) {
                    qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                    qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                    outerOaEnterpriseBindEntity.setAppId(ConfigCenter.crmAppId);
                } else {
                    if (StringUtils.isEmpty(finalAppId)) {
                        List<QyweixinCorpBindBo> filteredWxList = corpBindBoList.stream()
                                .filter(corpBindBo -> corpBindBo.getAppId().startsWith("wx"))
                                .sorted(Comparator.comparing(corpBindBo -> !corpBindBo.getAppId().equals(ConfigCenter.crmAppId)))
                                .collect(Collectors.toList());
                        List<QyweixinCorpBindBo> filteredDkList = corpBindBoList.stream()
                                .filter(corpBindBo -> corpBindBo.getAppId().startsWith("dk"))
                                .sorted(Comparator.comparing(corpBindBo -> !corpBindBo.getAppId().equals(ConfigCenter.repAppId)))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(filteredWxList) && CollectionUtils.isEmpty(filteredDkList)) {
                            qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                            qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                            outerOaEnterpriseBindEntity.setAppId(ConfigCenter.crmAppId);
                            log.info("migrateEnterpriseBind2 outerOaEnterpriseBindEntity,appId is null, outEa={}", outEa);
                        } else {
                            if (CollectionUtils.isNotEmpty(filteredWxList)) {
                                qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                                qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                                outerOaEnterpriseBindEntity.setAppId(filteredWxList.get(0).getAppId());
                                log.info("migrateEnterpriseBind2 outerOaEnterpriseBindEntity,appId is wx, outEa={}", outEa);
                                if (CollectionUtils.isNotEmpty(filteredDkList)) {
                                    qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                                    qywxConnectorVo.setFromAppId(filteredDkList.get(0).getAppId());
                                    log.info("migrateEnterpriseBind2 outerOaEnterpriseBindEntity, from appId is dk, outEa={}", outEa);
                                }
                            } else {
                                qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                                qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.serviceRepDev);
                                outerOaEnterpriseBindEntity.setAppId(filteredDkList.get(0).getAppId());
                                log.info("migrateEnterpriseBind2 outerOaEnterpriseBindEntity,appId is dk, outEa={}", outEa);
                            }
                        }
                    } else {
                        qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                        outerOaEnterpriseBindEntity.setAppId(finalAppId);
                        qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.serviceRepDev);
                        log.info("migrateEnterpriseBind2 outerOaEnterpriseBindEntity,appId is finalAppId, outEa={}", outEa);
                    }


                    //这里设置参数
                    QyweixinCorpBindBo corpBindBo = corpBindBoList.stream()
                            .filter(v1 -> v1.getAppId().equals(outerOaEnterpriseBindEntity.getAppId()))
                            .findFirst()
                            .orElse(null);

                    if (ObjectUtils.isNotEmpty(corpBindBo)) {
                        //回填应用参数，前端需要展示
                        qywxConnectorVo.setAgentId(Integer.valueOf(corpBindBo.getAgentId()));
                    }

                }
                outerOaEnterpriseBindEntity.setConnectInfo(JSON.toJSONString(qywxConnectorVo));
                List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = Lists.newArrayList(outerOaEnterpriseBindEntity);
                Integer count = outerOaEnterpriseBindManager.batchUpsert(enterpriseBindEntities);
                log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,count={}",count);
                if (count > 0) {
                    //更新配置信息
                    if (mapping.getBindType() == 0) {
                        //配置规则设置
                        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager
                                .queryConnectorConfigInfoByDcId(outerOaEnterpriseBindEntity.getId(), OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
                        if (ObjectUtils.isEmpty(configInfoEntity)) {
                            SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
//                            settingAccountRulesModel.setCurrentDcId(outerOaEnterpriseBindEntity.getId());
                            settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountSync);
                            settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                                    .unbind(Boolean.TRUE).stopEmp(Boolean.TRUE).build());
                            settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                                    .unbind(Boolean.TRUE).stopEmp(Boolean.TRUE).build());

                            configInfoEntity = OuterOaConfigInfoEntity.builder()
                                    .id(IdGenerator.get())
                                    .channel(ChannelEnum.qywx)
                                    .dcId(outerOaEnterpriseBindEntity.getId())
                                    .fsEa(outerOaEnterpriseBindEntity.getFsEa())
                                    .outEa(outerOaEnterpriseBindEntity.getOutEa())
                                    .appId(outerOaEnterpriseBindEntity.getAppId())
                                    .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                                    .configInfo(JSON.toJSONString(settingAccountRulesModel))
                                    .createTime(System.currentTimeMillis())
                                    .updateTime(System.currentTimeMillis())
                                    .build();
                            Integer ConfigCount = outerOaConfigInfoManager.insert(configInfoEntity);
                            log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,ConfigCount={}",ConfigCount);
                        }
                    } else {
                        //todo 在配置里的需要设置自动绑定
                        if (ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
                            //通过名称字段绑定
                            //配置规则设置
                            OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager
                                    .queryConnectorConfigInfoByDcId(outerOaEnterpriseBindEntity.getId(), OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
                            if (ObjectUtils.isEmpty(configInfoEntity)) {
                                SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
//                            settingAccountRulesModel.setCurrentDcId(outerOaEnterpriseBindEntity.getId());
                                settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountBind);
                                settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.auto);
                                settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                                        .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());
                                settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                                        .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());

                                configInfoEntity = OuterOaConfigInfoEntity.builder()
                                        .id(IdGenerator.get())
                                        .channel(ChannelEnum.qywx)
                                        .dcId(outerOaEnterpriseBindEntity.getId())
                                        .fsEa(outerOaEnterpriseBindEntity.getFsEa())
                                        .outEa(outerOaEnterpriseBindEntity.getOutEa())
                                        .appId(outerOaEnterpriseBindEntity.getAppId())
                                        .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                                        .configInfo(JSON.toJSONString(settingAccountRulesModel))
                                        .createTime(System.currentTimeMillis())
                                        .updateTime(System.currentTimeMillis())
                                        .build();
                                Integer ConfigCount = outerOaConfigInfoManager.insert(configInfoEntity);
                                log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,ConfigCount={}",ConfigCount);
                                //昵称映射
                                OuterOaConfigInfoEntity employeeUniqueIdentity = outerOaConfigInfoManager.getEntityByDataCenterId(
                                        OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, outerOaEnterpriseBindEntity.getId());
                                log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,employeeUniqueIdentity={}",employeeUniqueIdentity);
                            }
                        } else {
                            //配置规则设置
                            OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager
                                    .queryConnectorConfigInfoByDcId(outerOaEnterpriseBindEntity.getId(), OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
                            if (ObjectUtils.isEmpty(configInfoEntity)) {
                                SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
//                            settingAccountRulesModel.setCurrentDcId(outerOaEnterpriseBindEntity.getId());
                                settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountBind);
                                settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.manual);
                                settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                                        .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());
                                settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                                        .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());

                                configInfoEntity = OuterOaConfigInfoEntity.builder()
                                        .id(IdGenerator.get())
                                        .channel(ChannelEnum.qywx)
                                        .dcId(outerOaEnterpriseBindEntity.getId())
                                        .fsEa(outerOaEnterpriseBindEntity.getFsEa())
                                        .outEa(outerOaEnterpriseBindEntity.getOutEa())
                                        .appId(outerOaEnterpriseBindEntity.getAppId())
                                        .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                                        .configInfo(JSON.toJSONString(settingAccountRulesModel))
                                        .createTime(System.currentTimeMillis())
                                        .updateTime(System.currentTimeMillis())
                                        .build();
                                Integer ConfigCount = outerOaConfigInfoManager.insert(configInfoEntity);
                                log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,ConfigCount={}",ConfigCount);
                            }
                        }
                    }

                    //删除旧数据
                    int oldCount = qyweixinAccountEnterpriseBindDao.deleteEnterpriseBind(mapping.getOutEa(), mapping.getFsEa());
                    log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,delete oldCount={}",oldCount);
                    //成功才处理
                    migrateAppBind2(enterpriseBindEntities.stream().map(OuterOaEnterpriseBindEntity::getOutEa).collect(Collectors.toList()));
                    migrateEmpBind2(enterpriseBindEntities);
                    migrateDeptBind2(enterpriseBindEntities);
                    migrateConfigInfo2(enterpriseBindEntities);
                    migrateOrderInfo2(enterpriseBindEntities);
                    migrateBusinessInfoBind2(enterpriseBindEntities);
                    migrateQyweixinIdToOpenid2(enterpriseBindEntities);
                    migrateFileInfo2(enterpriseBindEntities);
                    migrateExternalContacts2(enterpriseBindEntities);
                    Thread.sleep(100);
                } else {
                    log.info("migrateEnterpriseBind2 error,count={}",count);
                }
            }
        } catch (Exception e) {
            log.info("migrateEnterpriseBind2 error,error={}", e.getMessage());
        }
    }

    @Override
    public Result<Integer> deleteQYWXAccountBind(String outEa, String fsEa) {
        log.info("QyweixinAccountSyncServiceImpl.deleteQYWXAccountBind,outEa={},fsEa={}",outEa,fsEa);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).fsEa(fsEa).build());
        log.info("QyweixinAccountSyncServiceImpl.deleteQYWXAccountBind,enterpriseBindEntities={}",enterpriseBindEntities);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
        }

        if (enterpriseBindEntities.size() > 10) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }

        Integer count = outerOaEnterpriseBindManager.deleteById(enterpriseBindEntities.get(0));
        log.info("QyweixinAccountSyncServiceImpl.deleteQYWXAccountBind,count={}",count);
        return new Result<>(count);
    }

    @Override
    public Result<Void> migrateOnlyEnterpriseBind(List<String> outEaList) {
        log.info("migrateOnlyEnterpriseBind start, outEaList:{}", outEaList);
        try {
            List<QyweixinAccountEnterpriseMapping> qyweixinAccountEnterpriseMappings = qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEaList(outEaList);

            for (QyweixinAccountEnterpriseMapping mapping : qyweixinAccountEnterpriseMappings) {
                log.info("migrateOnlyEnterpriseBind start, mapping:{}", mapping);
                String outEa = mapping.getOutEa();
                String fsEa = mapping.getFsEa();

                OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = new OuterOaEnterpriseBindEntity();
                outerOaEnterpriseBindEntity.setId(IdGenerator.get());
                outerOaEnterpriseBindEntity.setChannel(ChannelEnum.qywx);
                outerOaEnterpriseBindEntity.setOutEa(outEa);
                outerOaEnterpriseBindEntity.setFsEa(fsEa);
                outerOaEnterpriseBindEntity.setBindType(mapping.getBindType() == 1 ? BindTypeEnum.manual : BindTypeEnum.auto);
                outerOaEnterpriseBindEntity.setBindStatus(mapping.getStatus() == 0 ? BindStatusEnum.normal : (mapping.getStatus() == 1 ? BindStatusEnum.stop : BindStatusEnum.create));
                QywxConnectorVo qywxConnectorVo = new QywxConnectorVo();
                qywxConnectorVo.setDataCenterId(outerOaEnterpriseBindEntity.getId());
                qywxConnectorVo.setConnectorName("企业微信"); //ignoreI18n
                qywxConnectorVo.setDataCenterName("企业微信"); //ignoreI18n
                qywxConnectorVo.setChannel(ChannelEnum.qywx);
                //待办提醒默认为开启
                qywxConnectorVo.setAlertConfig(true);
                qywxConnectorVo.setAlertTypes(Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION));
                QyweixinCorpInfoBo qyweixinCorpInfoBo = qyweixinCorpInfoDao.queryCorpBindByCorpId(outEa);
                if (ObjectUtils.isNotEmpty(qyweixinCorpInfoBo)) {
                    qywxConnectorVo.setQywxEnterpriseName(qyweixinCorpInfoBo.getCorpName());
                }
                qywxConnectorVo.setQywxCorpId(mapping.getOutEa());
                qywxConnectorVo.setQywxDepartmentId(mapping.getDepId());
                qywxConnectorVo.setDomain(mapping.getDomain());
                EnterpriseExtendModel extendModel = JSON.parseObject(mapping.getExtend(), EnterpriseExtendModel.class);
                if (ObjectUtils.isNotEmpty(extendModel)) {
                    qywxConnectorVo.setIsFirstLand(extendModel.getIsFirstLand());
                    qywxConnectorVo.setIsRetainInformation(extendModel.getIsRetainInformation());
                }

                //appId从info表里找
                List<QyweixinCorpBindBo> corpBindBoList = qyweixinCorpBindDao.getByOutEa(outEa);
                //没有，默认为crmappId
                if (CollectionUtils.isEmpty(corpBindBoList)) {
                    qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                    qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                    outerOaEnterpriseBindEntity.setAppId(ConfigCenter.crmAppId);
                } else {
                    List<QyweixinCorpBindBo> filteredWxList = corpBindBoList.stream()
                            .filter(corpBindBo -> corpBindBo.getAppId().startsWith("wx"))
                            .sorted(Comparator.comparing(corpBindBo -> !corpBindBo.getAppId().equals(ConfigCenter.crmAppId)))
                            .collect(Collectors.toList());
                    List<QyweixinCorpBindBo> filteredDkList = corpBindBoList.stream()
                            .filter(corpBindBo -> corpBindBo.getAppId().startsWith("dk"))
                            .sorted(Comparator.comparing(corpBindBo -> !corpBindBo.getAppId().equals(ConfigCenter.repAppId)))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filteredWxList) && CollectionUtils.isEmpty(filteredDkList)) {
                        qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                        qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                        outerOaEnterpriseBindEntity.setAppId(ConfigCenter.crmAppId);
                        log.info("migrateOnlyEnterpriseBind outerOaEnterpriseBindEntity,appId is null, outEa={}", outEa);
                    } else {
                        if (CollectionUtils.isNotEmpty(filteredWxList)) {
                            qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                            qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                            outerOaEnterpriseBindEntity.setAppId(filteredWxList.get(0).getAppId());
                            log.info("migrateOnlyEnterpriseBind outerOaEnterpriseBindEntity,appId is wx, outEa={}", outEa);
                            if (CollectionUtils.isNotEmpty(filteredDkList)) {
                                qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                                qywxConnectorVo.setFromAppId(filteredDkList.get(0).getAppId());
                                log.info("migrateOnlyEnterpriseBind outerOaEnterpriseBindEntity, from appId is dk, outEa={}", outEa);
                            }
                        } else {
                            qywxConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
                            qywxConnectorVo.setAppType(OuterOaAppInfoTypeEnum.serviceRepDev);
                            outerOaEnterpriseBindEntity.setAppId(filteredDkList.get(0).getAppId());
                            log.info("migrateOnlyEnterpriseBind outerOaEnterpriseBindEntity,appId is dk, outEa={}", outEa);
                        }
                    }

                    //这里设置参数
                    QyweixinCorpBindBo corpBindBo = corpBindBoList.stream()
                            .filter(v1 -> v1.getAppId().equals(outerOaEnterpriseBindEntity.getAppId()))
                            .findFirst()
                            .orElse(null);

                    if (ObjectUtils.isNotEmpty(corpBindBo)) {
                        //回填应用参数，前端需要展示
                        qywxConnectorVo.setAgentId(Integer.valueOf(corpBindBo.getAgentId()));
                    }
                }
                outerOaEnterpriseBindEntity.setConnectInfo(JSON.toJSONString(qywxConnectorVo));
                List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = Lists.newArrayList(outerOaEnterpriseBindEntity);
                Integer count = outerOaEnterpriseBindManager.batchUpsert(enterpriseBindEntities);
                log.info("migrateOnlyEnterpriseBind.updateEnterpriseExtend,count={}",count);
                if (count > 0) {
                    //更新配置信息
                    if (mapping.getBindType() == 0) {
                        //配置规则设置
                        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager
                                .queryConnectorConfigInfoByDcId(outerOaEnterpriseBindEntity.getId(), OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
                        if (ObjectUtils.isEmpty(configInfoEntity)) {
                            SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
//                            settingAccountRulesModel.setCurrentDcId(outerOaEnterpriseBindEntity.getId());
                            settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountSync);
                            settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                                    .unbind(Boolean.TRUE).stopEmp(Boolean.TRUE).build());
                            settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                                    .unbind(Boolean.TRUE).stopEmp(Boolean.TRUE).build());

                            configInfoEntity = OuterOaConfigInfoEntity.builder()
                                    .id(IdGenerator.get())
                                    .channel(ChannelEnum.qywx)
                                    .dcId(outerOaEnterpriseBindEntity.getId())
                                    .fsEa(outerOaEnterpriseBindEntity.getFsEa())
                                    .outEa(outerOaEnterpriseBindEntity.getOutEa())
                                    .appId(outerOaEnterpriseBindEntity.getAppId())
                                    .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                                    .configInfo(JSON.toJSONString(settingAccountRulesModel))
                                    .createTime(System.currentTimeMillis())
                                    .updateTime(System.currentTimeMillis())
                                    .build();
                            Integer ConfigCount = outerOaConfigInfoManager.insert(configInfoEntity);
                            log.info("migrateOnlyEnterpriseBind.updateEnterpriseExtend,ConfigCount={}",ConfigCount);
                        }
                    } else {
                        //todo 在配置里的需要设置自动绑定
                        //配置规则设置
                        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager
                                .queryConnectorConfigInfoByDcId(outerOaEnterpriseBindEntity.getId(), OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
                        if (ObjectUtils.isEmpty(configInfoEntity)) {
                            SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
//                            settingAccountRulesModel.setCurrentDcId(outerOaEnterpriseBindEntity.getId());
                            settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountBind);
                            settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.manual);
                            settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                                    .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());
                            settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                                    .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());

                            configInfoEntity = OuterOaConfigInfoEntity.builder()
                                    .id(IdGenerator.get())
                                    .channel(ChannelEnum.qywx)
                                    .dcId(outerOaEnterpriseBindEntity.getId())
                                    .fsEa(outerOaEnterpriseBindEntity.getFsEa())
                                    .outEa(outerOaEnterpriseBindEntity.getOutEa())
                                    .appId(outerOaEnterpriseBindEntity.getAppId())
                                    .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                                    .configInfo(JSON.toJSONString(settingAccountRulesModel))
                                    .createTime(System.currentTimeMillis())
                                    .updateTime(System.currentTimeMillis())
                                    .build();
                            Integer ConfigCount = outerOaConfigInfoManager.insert(configInfoEntity);
                            log.info("migrateOnlyEnterpriseBind.updateEnterpriseExtend,ConfigCount={}",ConfigCount);
                        }
                    }

                    //删除旧数据
                    int oldCount = qyweixinAccountEnterpriseBindDao.deleteEnterpriseBind(mapping.getOutEa(), mapping.getFsEa());
                    log.info("migrateOnlyEnterpriseBind.updateEnterpriseExtend,delete oldCount={}",oldCount);
                } else {
                    log.info("migrateOnlyEnterpriseBind error,count={}",count);
                }
            }
        } catch (Exception e) {
            log.info("migrateOnlyEnterpriseBind error,error={}", e.getMessage());
        }
        return new Result<>();
    }

    @Override
    public Result<Void> cleanNewEnterpriseBind(String outEa, String appId, String fsEa) {
        //删除enterprise_bind表数据
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).fsEa(fsEa).build());
        log.info("cleanNewEnterpriseBind,enterpriseBindEntities={}",enterpriseBindEntities);
        if (CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
            Integer count = outerOaEnterpriseBindManager.deleteById(enterpriseBindEntities.get(0));
            log.info("cleanNewEnterpriseBind,enterpriseBindEntities,count={}",count);
        }
        List<OuterOaConfigInfoEntity> configInfoEntities = outerOaConfigInfoManager.getEntities(OuterOaConfigInfoParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).fsEa(fsEa).build());
        log.info("cleanNewEnterpriseBind,configInfoEntities={}",configInfoEntities);
        if (CollectionUtils.isNotEmpty(configInfoEntities)) {
            for (OuterOaConfigInfoEntity configInfoEntity : configInfoEntities) {
                Integer count = outerOaConfigInfoManager.deleteById(configInfoEntity);
                log.info("cleanNewEnterpriseBind,configInfoEntity,count={}",count);
            }
        }
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).fsEa(fsEa).build());
        log.info("cleanNewEnterpriseBind,employeeBindEntities={}",employeeBindEntities);
        if (CollectionUtils.isNotEmpty(employeeBindEntities)) {
            Integer count = outerOaEmployeeBindManager.batchDeleteByIds(employeeBindEntities.stream().map(OuterOaEmployeeBindEntity::getId).collect(Collectors.toList()));
            log.info("cleanNewEnterpriseBind,employeeBindEntity,count={}",count);
        }
        List<OuterOaDepartmentBindEntity> departmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).fsEa(fsEa).build());
        log.info("cleanNewEnterpriseBind,departmentBindManagerEntities={}",departmentBindManagerEntities);
        if (CollectionUtils.isNotEmpty(departmentBindManagerEntities)) {
            for (OuterOaDepartmentBindEntity departmentBindManagerEntity : departmentBindManagerEntities) {
                Integer count = outerOaDepartmentBindManager.deleteById(departmentBindManagerEntity);
                log.info("cleanNewEnterpriseBind,departmentBindManagerEntity,count={}",count);
            }
        }
        return new Result<>();
    }

    @Override
    public Result<Void> fixEnterpriseManualBindInfo() {
        //查出全部反绑定的企业
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).bindStatus(BindStatusEnum.normal).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            try {
                log.info("fixEnterpriseManualBindInfo,enterpriseBindEntity={}", enterpriseBindEntity);
                String outEa = enterpriseBindEntity.getOutEa();
                String fsEa = enterpriseBindEntity.getFsEa();
                String appId = enterpriseBindEntity.getAppId();
                String dcId = enterpriseBindEntity.getId();

                QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
                if (StringUtils.isEmpty(qywxConnectorVo.getQywxCorpId())) {
                    qywxConnectorVo.setQywxCorpId(outEa);
                }

                if (StringUtils.isEmpty(qywxConnectorVo.getDataCenterName())) {
                    qywxConnectorVo.setDataCenterName("企业微信"); //ignoreI18n
                }

                if (ObjectUtils.isEmpty(qywxConnectorVo.getAgentId())) {
                    OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, outEa, appId);
                    if (ObjectUtils.isNotEmpty(appInfoEntity)) {
                        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
                        if (ObjectUtils.isNotEmpty(qyweixinAppInfoParams)
                                && ObjectUtils.isNotEmpty(qyweixinAppInfoParams.getAuthAppInfo())
                                && ObjectUtils.isNotEmpty(qyweixinAppInfoParams.getAuthAppInfo().getAgentId())) {
                            qywxConnectorVo.setAgentId(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());
                        }
                    }
                }
                if (ObjectUtils.isEmpty(qywxConnectorVo.getAgentId())) {
                    //找老库
                    QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEa, appId);
                    if (ObjectUtils.isNotEmpty(qyweixinCorpBindBo)) {
                        qywxConnectorVo.setAgentId(Integer.valueOf(qyweixinCorpBindBo.getAgentId()));
                    }
                }

                enterpriseBindEntity.setConnectInfo(JSON.toJSONString(qywxConnectorVo));
                log.info("fixEnterpriseManualBindInfo,dcId={},qywxConnectorVo={}.", dcId, qywxConnectorVo);
                Integer bindCount = outerOaEnterpriseBindManager.updateById(enterpriseBindEntity);
                log.info("fixEnterpriseManualBindInfo,bindCount={}.", bindCount);

                //config
                if (enterpriseBindEntity.getBindType() == BindTypeEnum.manual) {
                    OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.queryConnectorConfigInfoByDcId(dcId, OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
                    if (ObjectUtils.isNotEmpty(configInfoEntity)) {
                        SettingAccountRulesModel settingAccountRulesModel =
                                JSON.parseObject(configInfoEntity.getConfigInfo(), SettingAccountRulesModel.class);
                        if (ObjectUtils.isEmpty(settingAccountRulesModel.getEmployeeLeaveRule())) {
                            settingAccountRulesModel.setEmployeeLeaveRule(SettingAccountRulesModel.EmployeeLeaveRule.builder()
                                    .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());
                        }
                        if (ObjectUtils.isEmpty(settingAccountRulesModel.getEmployeeRangeRemoveRule())) {
                            settingAccountRulesModel.setEmployeeRangeRemoveRule(SettingAccountRulesModel.EmployeeRangeRemoveRule.builder()
                                    .unbind(Boolean.FALSE).stopEmp(Boolean.FALSE).build());
                        }

                        configInfoEntity.setConfigInfo(JSON.toJSONString(settingAccountRulesModel));
                        log.info("fixEnterpriseManualBindInfo,settingAccountRulesModel={}.", settingAccountRulesModel);
                        Integer count = outerOaConfigInfoManager.updateById(configInfoEntity);
                        log.info("fixEnterpriseManualBindInfo,configInfoEntity,count={}",count);
                    }
                }
            } catch (Exception e) {
                log.info("fixEnterpriseManualBindInfo error,error={}", e.getMessage());
            }
        }

        return new Result<>();
    }

    @Override
    public Result<Map<String, String>> migrateEnterpriseBindSql(String outEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        StringBuilder sqlBuilder = new StringBuilder();

        // 企业绑定
        sqlBuilder.append("INSERT INTO outer_oa_enterprise_bind (\n");
        sqlBuilder.append("    id, channel, fs_ea, out_ea, app_id, connect_info, bind_type, bind_status, create_time, update_time\n");
        sqlBuilder.append(") VALUES \n");

        for (int i = 0; i < enterpriseBindEntities.size(); i++) {
            OuterOaEnterpriseBindEntity entity = enterpriseBindEntities.get(i);
            sqlBuilder.append("    ('").append(entity.getId()).append("', ");
            sqlBuilder.append("'").append(entity.getChannel()).append("', ");
            sqlBuilder.append("'").append(entity.getFsEa()).append("', ");
            sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
            sqlBuilder.append("'").append(entity.getAppId()).append("', ");
            sqlBuilder.append("'").append(entity.getConnectInfo().replace("'", "''")).append("', ");
            sqlBuilder.append("'").append(entity.getBindType()).append("', ");
            sqlBuilder.append("'").append(entity.getBindStatus()).append("', ");
            sqlBuilder.append(entity.getCreateTime()).append(", ");
            sqlBuilder.append(entity.getUpdateTime()).append(")");

            // 如果不是最后一条记录，添加逗号和换行
            if (i < enterpriseBindEntities.size() - 1) {
                sqlBuilder.append(",\n");
            } else {
                sqlBuilder.append(";\n");
            }
        }

        Map<String, String> result = new HashMap<>();
        log.info("migrateEnterpriseBindSql.OuterOaEnterpriseBindEntity,sql={}", sqlBuilder.toString());
        result.put("企业绑定", sqlBuilder.toString());

        //配置文件
        List<OuterOaConfigInfoEntity> configInfoEntities = outerOaConfigInfoManager.getEntities(OuterOaConfigInfoParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if (CollectionUtils.isNotEmpty(configInfoEntities)) {
            sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO outer_oa_config_info (\n");
            sqlBuilder.append("    id, channel, dc_id, fs_ea, out_ea, app_id, type, config_info, create_time, update_time\n");
            sqlBuilder.append(") VALUES \n");

            for (int i = 0; i < configInfoEntities.size(); i++) {
                OuterOaConfigInfoEntity entity = configInfoEntities.get(i);
                sqlBuilder.append("    ('").append(entity.getId()).append("', ");
                sqlBuilder.append("'").append(entity.getChannel()).append("', ");
                sqlBuilder.append("'").append(entity.getDcId()).append("', ");
                sqlBuilder.append("'").append(entity.getFsEa()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
                sqlBuilder.append("'").append(entity.getAppId()).append("', ");
                sqlBuilder.append("'").append(entity.getType()).append("', ");
                sqlBuilder.append("'").append(entity.getConfigInfo().replace("'", "''")).append("', ");
                sqlBuilder.append(entity.getCreateTime()).append(", ");
                sqlBuilder.append(entity.getUpdateTime()).append(")");

                if (i < configInfoEntities.size() - 1) {
                    sqlBuilder.append(",\n");
                } else {
                    sqlBuilder.append(";\n");
                }
            }
            log.info("migrateEnterpriseBindSql.OuterOaConfigInfoEntity,sql={}", sqlBuilder.toString());
            result.put("配置信息", sqlBuilder.toString());
        }

        //应用信息
        List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if (CollectionUtils.isNotEmpty(appInfoEntities)) {
            sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO outer_oa_app_info (\n");
            sqlBuilder.append("    id, channel, out_ea, app_id, app_type, app_info, status, create_time, update_time\n");
            sqlBuilder.append(") VALUES \n");

            for (int i = 0; i < appInfoEntities.size(); i++) {
                OuterOaAppInfoEntity entity = appInfoEntities.get(i);
                sqlBuilder.append("    ('").append(entity.getId()).append("', ");
                sqlBuilder.append("'").append(entity.getChannel()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
                sqlBuilder.append("'").append(entity.getAppId()).append("', ");
                sqlBuilder.append("'").append(entity.getAppType()).append("', ");
                sqlBuilder.append("'").append(entity.getAppInfo().replace("'", "''")).append("', ");
                sqlBuilder.append("'").append(entity.getStatus()).append("', ");
                sqlBuilder.append(entity.getCreateTime()).append(", ");
                sqlBuilder.append(entity.getUpdateTime()).append(")");

                if (i < appInfoEntities.size() - 1) {
                    sqlBuilder.append(",\n");
                } else {
                    sqlBuilder.append(";\n");
                }
            }
            log.info("migrateEnterpriseBindSql.OuterOaAppInfoEntity,sql={}", sqlBuilder.toString());
            result.put("应用信息", sqlBuilder.toString());
        }

        //人员信息
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if (CollectionUtils.isNotEmpty(employeeBindEntities)) {
            sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO outer_oa_employee_bind (\n");
            sqlBuilder.append("    id, channel, dc_id, fs_ea, out_ea, app_id, fs_emp_id, out_emp_id, bind_status, create_time, update_time\n");
            sqlBuilder.append(") VALUES \n");

            for (int i = 0; i < employeeBindEntities.size(); i++) {
                OuterOaEmployeeBindEntity entity = employeeBindEntities.get(i);
                sqlBuilder.append("    ('").append(entity.getId()).append("', ");
                sqlBuilder.append("'").append(entity.getChannel()).append("', ");
                sqlBuilder.append("'").append(entity.getDcId()).append("', ");
                sqlBuilder.append("'").append(entity.getFsEa()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
                sqlBuilder.append("'").append(entity.getAppId()).append("', ");
                sqlBuilder.append("'").append(entity.getFsEmpId()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEmpId()).append("', ");
                sqlBuilder.append("'").append(entity.getBindStatus()).append("', ");
                sqlBuilder.append(entity.getCreateTime()).append(", ");
                sqlBuilder.append(entity.getUpdateTime()).append(")");

                if (i < employeeBindEntities.size() - 1) {
                    sqlBuilder.append(",\n");
                } else {
                    sqlBuilder.append(";\n");
                }
            }
            log.info("migrateEnterpriseBindSql.OuterOaEmployeeBindEntity,sql={}", sqlBuilder.toString());
            result.put("人员绑定", sqlBuilder.toString());
        }

        //部门信息
        List<OuterOaDepartmentBindEntity> departmentBindEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if (CollectionUtils.isNotEmpty(departmentBindEntities)) {
            sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO outer_oa_department_bind (\n");
            sqlBuilder.append("    id, channel, dc_id, fs_ea, out_ea, app_id, fs_dep_id, out_dep_id, bind_status, create_time, update_time\n");
            sqlBuilder.append(") VALUES \n");

            for (int i = 0; i < departmentBindEntities.size(); i++) {
                OuterOaDepartmentBindEntity entity = departmentBindEntities.get(i);
                sqlBuilder.append("    ('").append(entity.getId()).append("', ");
                sqlBuilder.append("'").append(entity.getChannel()).append("', ");
                sqlBuilder.append("'").append(entity.getDcId()).append("', ");
                sqlBuilder.append("'").append(entity.getFsEa()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
                sqlBuilder.append("'").append(entity.getAppId()).append("', ");
                sqlBuilder.append("'").append(entity.getFsDepId()).append("', ");
                sqlBuilder.append("'").append(entity.getOutDepId()).append("', ");
                sqlBuilder.append("'").append(entity.getBindStatus()).append("', ");
                sqlBuilder.append(entity.getCreateTime()).append(", ");
                sqlBuilder.append(entity.getUpdateTime()).append(")");

                if (i < departmentBindEntities.size() - 1) {
                    sqlBuilder.append(",\n");
                } else {
                    sqlBuilder.append(";\n");
                }
            }
            log.info("migrateEnterpriseBindSql.OuterOaDepartmentBindEntity,sql={}", sqlBuilder.toString());
            result.put("部门绑定", sqlBuilder.toString());
        }

        //外部联系人
        List<OuterOaExternalContactsEntity> externalContactsEntities = outerOaExternalContactsManager.getEntities(OuterOaExternalContactsParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if (CollectionUtils.isNotEmpty(externalContactsEntities)) {
            sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO outer_oa_external_contacts (\n");
            sqlBuilder.append("    id, channel, out_ea, out_user_id, external_user_id, external_name, avatar, external_contacts_info, create_time, update_time\n");
            sqlBuilder.append(") VALUES \n");

            for (int i = 0; i < externalContactsEntities.size(); i++) {
                OuterOaExternalContactsEntity entity = externalContactsEntities.get(i);
                sqlBuilder.append("    ('").append(entity.getId()).append("', ");
                sqlBuilder.append("'").append(entity.getChannel()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
                sqlBuilder.append("'").append(entity.getOutUserId()).append("', ");
                sqlBuilder.append("'").append(entity.getExternalUserId()).append("', ");
                sqlBuilder.append("'").append(entity.getExternalName()).append("', ");
                sqlBuilder.append("'").append(entity.getAvatar()).append("', ");
                sqlBuilder.append("'").append(entity.getExternalContactsInfo().replace("'", "''")).append("', ");
                sqlBuilder.append(entity.getCreateTime()).append(", ");
                sqlBuilder.append(entity.getUpdateTime()).append(")");

                if (i < externalContactsEntities.size() - 1) {
                    sqlBuilder.append(",\n");
                } else {
                    sqlBuilder.append(";\n");
                }
            }
            log.info("migrateEnterpriseBindSql.OuterOaExternalContactsEntity,sql={}", sqlBuilder.toString());
            result.put("外部联系人", sqlBuilder.toString());
        }

        //qyweixin_id_to_openid
        List<QyweixinIdToOpenidEntity> idToOpenidEntities = qyweixinIdToOpenidManager.getEntities(QyweixinIdToOpenidParams.builder().outEa(outEa).build());
        if (CollectionUtils.isNotEmpty(idToOpenidEntities)) {
            sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO qyweixin_id_to_openid (\n");
            sqlBuilder.append("    id, out_ea, plaintext_id, openid, type, create_time, update_time\n");
            sqlBuilder.append(") VALUES \n");

            for (int i = 0; i < idToOpenidEntities.size(); i++) {
                QyweixinIdToOpenidEntity entity = idToOpenidEntities.get(i);
                sqlBuilder.append("    ('").append(entity.getId()).append("', ");
                sqlBuilder.append("'").append(entity.getOutEa()).append("', ");
                sqlBuilder.append("'").append(entity.getPlaintextId()).append("', ");
                sqlBuilder.append("'").append(entity.getOpenid()).append("', ");
                sqlBuilder.append(entity.getType()).append(", ");
                sqlBuilder.append(entity.getCreateTime()).append(", ");
                sqlBuilder.append(entity.getUpdateTime()).append(")");

                if (i < idToOpenidEntities.size() - 1) {
                    sqlBuilder.append(",\n");
                } else {
                    sqlBuilder.append(";\n");
                }
            }
            log.info("migrateEnterpriseBindSql.QyweixinIdToOpenidEntity,sql={}", sqlBuilder.toString());
            result.put("用户ID映射", sqlBuilder.toString());
        }

        return new Result<>(result);
    }
}
