package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityObj;
import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/20
 */
@Data
@SecurityObj
@Table(name = "qywexin_business_info_bind")
public class QyweixinBusinessInfoBindBo extends IdEntity implements Serializable {

    private String fsEa;

    /**
     * 专门用于代开发转换的第三方corpId
     */
    private String outEa;

    private String businessType;

    private String appId;


    private Integer status; //0-正常，1-停用

    private Timestamp createTime;

    private Timestamp updateTime;
}
