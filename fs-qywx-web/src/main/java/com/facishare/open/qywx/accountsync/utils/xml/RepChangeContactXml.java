package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@XStreamAlias("xml")
@Data
public class RepChangeContactXml {
    @XStreamAlias("ToUserName")
    private String ToUserName;

    @XStreamAlias("FromUserName")
    private String FromUserName;

    @XStreamAlias("CreateTime")
    private String CreateTime;

    @XStreamAlias("MsgType")
    private String MsgType;

    @XStreamAlias("Event")
    private String Event;

    @XStreamAlias("ChangeType")
    private String ChangeType;

    @XStreamAlias("UserID")
    private String UserID;

    @XStreamAlias("Name")
    private String Name;

    @XStreamAlias("Id")
    private String Id;
}
