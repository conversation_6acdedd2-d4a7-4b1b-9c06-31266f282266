package com.facishare.open.qywx.web.db.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinBusinessInfoBindBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QyweixinBusinessBindInfoDao extends ICrudMapper<QyweixinBusinessInfoBindBo> {
    @Update("<script>" +
            "insert into qywexin_business_info_bind(id, fs_ea, out_ea, app_id, business_type, status, create_time, update_time) " +
            "values " +
            "<foreach collection=\"list\" item=\"item\" separator=\",\" >\n" +
            "    (\n" +
            "    #{item.id,jdbcType=INTEGER}, #{item.fsEa,jdbcType=INTEGER},\n" +
            "    #{item.outEa},\n" +
            "    #{item.appId}, #{item.businessType}, #{item.status},\n" +
            "    NOW(), NOW()\n" +
            "    )\n" +
            "</foreach>" +
            "on duplicate key update " +
            "status = values(status), " +
            "update_time = NOW() " +
            "</script>")
    int batchUpsertInfos(@Param("list") List<QyweixinBusinessInfoBindBo> businessInfoBindBos);

    @Select("<script>" + "select * from qywexin_business_info_bind where out_ea=#{outEa}" + "</script>")
    List<QyweixinBusinessInfoBindBo> findOutEa(@Param("outEa") String outEa);

    /**
     * 根据企业ID分页查询业务信息绑定数据
     *
     * @param outEa 企业ID
     * @param offset 偏移量
     * @param limit 每页大小
     * @return 业务信息绑定列表
     */
    @Select("SELECT * " +
            "FROM qywexin_business_info_bind " +
            "WHERE out_ea = #{outEa} " +
            "ORDER BY id " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<QyweixinBusinessInfoBindBo> queryBusinessInfoBindByOutEa(@Param("outEa") String outEa,
                                                                  @Param("offset") int offset,
                                                                  @Param("limit") int limit);
}
