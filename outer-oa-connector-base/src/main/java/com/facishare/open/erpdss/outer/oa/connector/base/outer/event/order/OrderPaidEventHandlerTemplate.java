package com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单事件处理器模板
 * <AUTHOR>
 * @date 2024-08-19
 */

@Slf4j
public abstract class OrderPaidEventHandlerTemplate extends HandlerTemplate {
    protected static final String OPEN_ENTERPRISE = "open_enterprise";
    protected static final String SAVE_ORDER = "save_order";
    protected static final String NOT_SUPPORT = "not_support";
    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        openEnterpriseOrSaveOrder(context);
        log.info("OrderPaidEventHandlerTemplate.execute,context={}",context);
        if(context.isError()) {
            return context.getResult();
        }

        String result = context.getResult().getData().toString();
        if(StringUtils.equalsIgnoreCase(result,OPEN_ENTERPRISE)) {
            openEnterprise(context);
            log.info("OrderPaidEventHandlerTemplate.execute,openEnterprise,context={}",context);
        }
        else if(StringUtils.equalsIgnoreCase(result,SAVE_ORDER)) {
            saveOrder(context);
            log.info("OrderPaidEventHandlerTemplate.execute,saveOrder,context={}",context);
        } else {
            log.info("OrderPaidEventHandlerTemplate.execute,event={} not supported",result);
            context.getResult().setCode(-1);
            context.getResult().setMsg(NOT_SUPPORT);
        }
        log.info("OrderPaidEventHandlerTemplate.execute,context.end={}",context);
        return context.getResult();
    }

    /**
     * 这个方法，用来判断是否要开通企业，只有首次下单或安装应用，才需要开通企业，需要返回 OPEN_ENTERPRISE，
     * 后续新增订单，都不需要开通企业，需要返回 SAVE_ORDER
     * @param context
     */
    public abstract void openEnterpriseOrSaveOrder(MethodContext context);

    /**
     * 开通企业需要执行的方法
     * @param context
     */
    public abstract void openEnterprise(MethodContext context);

    /**
     * 保存订单需要执行的方法
     * @param context
     */
    public abstract void saveOrder(MethodContext context);
}
