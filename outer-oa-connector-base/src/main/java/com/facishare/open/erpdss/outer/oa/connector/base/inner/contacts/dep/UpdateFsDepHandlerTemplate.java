package com.facishare.open.erpdss.outer.oa.connector.base.inner.contacts.dep;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.field_mapping.EmpAndDepFieldMappingTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 更新纷享部门处理器模板
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public abstract class UpdateFsDepHandlerTemplate extends HandlerTemplate {
    @Resource
    private EmpAndDepFieldMappingTemplate empAndDepFieldMappingTemplate;

    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        if(empAndDepFieldMappingTemplate!=null) {
            empAndDepFieldMappingTemplate.depFieldMapping(context);
            log.info("UpdateFsDepHandlerTemplate.execute,depFieldMapping,context={}",context);
            if(context.isError()) {
                return context.getResult();
            }
        }

        updateDep(context);
        log.info("UpdateFsDepHandlerTemplate.execute,updateDep,context={}",context);

        return context.getResult();
    }

    /**
     * 更新部门
     * @param context
     */
    public abstract void updateDep(MethodContext context);
}
