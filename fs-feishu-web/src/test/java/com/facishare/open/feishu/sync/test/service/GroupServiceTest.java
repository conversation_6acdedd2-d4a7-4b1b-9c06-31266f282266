package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.model.Group.GetGroupMemberData;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.GroupService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class GroupServiceTest extends BaseTest {

    @Autowired
    private GroupService groupService;

    @Test
    public void test(){
        Result<GetGroupMemberData> groupMemberList = groupService.getGroupMemberList("cli_a20192f6afb8d00c", "11f8e072d486575f","8318dgf82ab42d2c", 10, "", null, "user");
        System.out.println(groupMemberList.getData());
    }
}
