package com.facishare.open.feishu.sync.test.mongo;

import com.facishare.open.oa.base.dbproxy.mongo.dao.FsUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsUserInfoDoc;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.mongodb.client.result.DeleteResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

public class FsUserInfoMongoDaoTest extends BaseTest {
    @Autowired
    private FsUserInfoMongoDao fsUserInfoMongoDao;

    @Test
    public void countDocuments() {
        Long counts = fsUserInfoMongoDao.countDocuments("fszdbd2575");
        System.out.println(counts);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        long start = System.currentTimeMillis();
        DeleteResult deleteResult = fsUserInfoMongoDao.deleteNotInCollectionDocs("85903", new LinkedList<>());
        long end = System.currentTimeMillis() - start;
        System.out.println("rrrrr"+ end);
        System.out.println(deleteResult);
    }

    @Test
    public void queryUserInfosByIds() {
        List<Integer> a = new LinkedList<>();
        for (int i = 0; i < 000; i++) {
            a.add(i);
        }
        long start = System.currentTimeMillis();
        List<FsUserInfoDoc> docs = fsUserInfoMongoDao.queryUserInfosByIds("tests001", 1, a);
        long end = System.currentTimeMillis() - start;
        System.out.println("rrrrr"+ end);
        System.out.println(docs);
    }

    @Test
    public void queryUserInfosByNotIds() {
//        List<Integer> a = new LinkedList<>();
//        for (int i = 0; i < 10; i++) {
//            a.add(i);
//        }
        long start = System.currentTimeMillis();
        List<FsUserInfoDoc> docs = fsUserInfoMongoDao.queryUserInfosByNotIds("tests001", 1, new LinkedList<>());
        long end = System.currentTimeMillis() - start;
        System.out.println("rrrrr"+ end);
        System.out.println(docs);
    }

    @Test
    public void queryUserInfos() {
        long start = System.currentTimeMillis();
        List<FsUserInfoDoc> docs = fsUserInfoMongoDao.queryUserInfos("tests001", 1);
        long end = System.currentTimeMillis() - start;
        System.out.println("rrrrr"+ end);
        System.out.println(docs);
    }

    @Test
    public void addIndex() {
        fsUserInfoMongoDao.addIndex();
    }
}
