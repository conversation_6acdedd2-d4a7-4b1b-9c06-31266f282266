package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.manager.OAConnectorDataManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OAConnectorDataManagerTest extends BaseTest {
    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;

    @Test
    public void sendTest() {
        oaConnectorDataManager.send(null, null, "feishu", "suite_ticket", "1231sdfgv-f_zdsgdfd", null, "853005", "飞书suite_ticket缓存失效");
    }

}
