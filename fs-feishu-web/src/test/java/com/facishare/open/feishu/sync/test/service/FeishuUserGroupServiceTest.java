package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.enums.MemberTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserGroupMemberData;
import com.facishare.open.feishu.syncapi.service.FeishuDepartmentService;
import com.facishare.open.feishu.syncapi.service.FeishuUserGroupService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class FeishuUserGroupServiceTest  extends BaseTest {
    @Resource
    private FeishuUserGroupService feishuUserGroupService;
    @Resource
    private FeishuDepartmentService feishuDepartmentService;

    @Test
    public void getUserGroupMemberList() {
        Result<List<UserGroupMemberData>> result = feishuUserGroupService.getUserGroupMemberList(testAppId,
                testTenantKey,
                "7837a79fc3c54bae",
                MemberTypeEnum.user);
        result = feishuUserGroupService.getUserGroupMemberList(testAppId,
                testTenantKey,
                "7837a79fc3c54bae",
                MemberTypeEnum.department);
        Result<DepartmentData.Department> deptInfo = feishuDepartmentService.getDeptInfo(testAppId,
                testTenantKey,
                result.getData().get(0).getMemberId());

        System.out.println(result);
    }
}
