package com.facishare.open.feishu.sync.test.manager;

import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

public class EmployeeBindManagerTest {
    @Resource
    private EmployeeBindManager employeeBindManager;

    @Test
    public void batchUpdateBindStatus() {
        int status = employeeBindManager.batchUpdateBindStatus("fsea",
                Lists.newArrayList(GlobalValue.FS_ADMIN_USER_ID+""),
                BindStatusEnum.stop,
                null);
        System.out.println(status);
    }
}
