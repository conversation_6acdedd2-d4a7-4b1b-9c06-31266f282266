package com.facishare.open.feishu.web.handler;

import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.web.template.outer.event.ticket.FeishuTicketEventHandlerTemplate;
import com.facishare.open.feishu.web.template.model.FeishuEventHandleModel;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * app_ticket 事件事件处理器
 */
@Component
public class AppTicketEventHandler extends FeishuEventHandler {
    @Resource
    private FeishuTicketEventHandlerTemplate feishuTicketEventHandlerTemplate;

    @Override
    public String getSupportEventType() {
        return "app_ticket";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        FeishuEventHandleModel eventHandleModel = new FeishuEventHandleModel();
        eventHandleModel.setHeader(header);
        eventHandleModel.setEventType(getSupportEventType());
        eventHandleModel.setEventData(eventData);

        return feishuTicketEventHandlerTemplate.execute(eventHandleModel).getData().toString();
    }
}
