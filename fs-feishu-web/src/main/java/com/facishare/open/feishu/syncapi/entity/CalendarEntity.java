package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_calendar_info")
public class CalendarEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 渠道
     */
    private ChannelEnum channel;
    /**
     * 外部企业账号id
     */
    private String outEa;
    /**
     * 日历id
     */
    private String calendarId;
    /**
     * 状态
     */
    private Integer status;

    private Date createTime;
    private Date updateTime;
}
