package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.feishu.syncapi.enums.AppStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_app_info")
public class AppInfoEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 企业唯一标识
     */
    private String tenantKey;
    /**
     * 应用状态 start_by_tenant: 租户启用; stop_by_tenant: 租户停用; stop_by_platform: 平台停用
     */
    private AppStatusEnum status;
    /**
     * "applicants": [ // 应用的申请者，可能有多个
     *             {
     *                 "open_id":"xxx" ,  // 用户对此应用的唯一标识，同一用户对不同应用的open_id不同
     *             }
     *         ],
     */
    private String applicants;
    /**
     * 当应用被管理员安装时，返回此字段。如果是自动安装或由普通成员获取时，没有此字段
     */
    private String installerOpenId;
    /**
     * 当应用被普通成员安装时，返回此字段
     */
    private String installerEmployeeOpenId;
    /**
     * "operator": { // 仅status=start_by_tenant时有此字段
     *              "open_id":"xxx",
     *              "user_id":"yyy", // 仅自建应用才会返回
     *              "union_id": "zzz" // 用户在ISV下的唯一标识
     *         },
     */
    private String operator;
    private String clientId;
    private String clientSecret;
    private Date createTime;
    private Date updateTime;
}
