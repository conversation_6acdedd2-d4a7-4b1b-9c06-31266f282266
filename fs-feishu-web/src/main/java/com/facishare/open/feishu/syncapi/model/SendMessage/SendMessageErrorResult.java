package com.facishare.open.feishu.syncapi.model.SendMessage;

import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;

@Data
public class SendMessageErrorResult implements Serializable {
    int code;
    String msg;
    SendMessageError error;

    @Data
    public static class SendMessageError implements Serializable {
        FieldViolations[] fieldViolations;

        @Override
        public String toString() {
            return "SendMessageError{" +
                    "fieldViolations=" + Arrays.toString(fieldViolations) +
                    '}';
        }
    }

    @Data
    public static class FieldViolations implements Serializable {
        String field;
        String description;

        @Override
        public String toString() {
            return "FieldViolations{" +
                    "field='" + field + '\'' +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "SendMessageErrorResult{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", error=" + error +
                '}';
    }
}
