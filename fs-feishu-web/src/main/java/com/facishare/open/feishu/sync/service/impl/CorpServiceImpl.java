package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.CorpInfoManager;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.CorpService;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("corpService")
public class CorpServiceImpl implements CorpService {
    @Resource
    private CorpInfoManager corpInfoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private FeishuTenantService feishuTenantService;

    @Override
    public Result<Void> updateCorpInfo(String appId, String tenantKey) {
        Result<QueryTenantInfoData> result = feishuTenantService.queryTenantInfo(appId, tenantKey);
        LogUtils.info("CorpServiceImpl.updateCorpInfo,result={}",result);
        if(result.code==0 && result.getData()!=null) {
            QueryTenantInfoData.Tenant tenant = result.getData().getTenant();
            CorpInfoEntity entity = CorpInfoEntity.builder()
                    .displayId(tenant.getDisplayId())
                    .tenantKey(tenant.getTenantKey())
                    .tenantName(tenant.getName())
                    .tenantTag(tenant.getTenantTag())
                    .build();
            corpInfoManager.updateCorpInfo(entity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<CorpInfoEntity> getCorpEntity(String tenantKey) {
        return Result.newSuccess(corpInfoManager.getEntityByTenantKey(tenantKey));
    }
}
