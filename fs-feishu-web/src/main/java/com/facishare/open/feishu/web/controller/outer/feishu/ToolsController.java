package com.facishare.open.feishu.web.controller.outer.feishu;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.ToolsManager;
import com.facishare.open.feishu.sync.mapper.EnterpriseBindMapper;
import com.facishare.open.outer.oa.connector.common.api.info.FsServiceAuthInfo;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.feishu.syncapi.service.ServiceAuthService;
import com.facishare.open.feishu.syncapi.service.ToolsService;
import com.facishare.open.feishu.web.template.outer.event.order.FeishuOrderPaidEventHandlerTemplate;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value="/feishu/tools")
public class ToolsController {
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private OrderService orderService;
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private ToolsService toolsService;
    @Resource
    private ServiceAuthService serviceAuthService;
    @Resource
    private FeishuOrderPaidEventHandlerTemplate feishuOrderPaidEventHandlerTemplate;
    @Autowired
    private ToolsManager toolsManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;

    @RequestMapping(value="/getRedisValue",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> getRedisValue(@RequestParam("key") String key) {
        return Result.newSuccess(redisDataSource.getRedisClient().get(key));
    }

    @RequestMapping(value="/orderPaid",method = RequestMethod.POST)
    @ResponseBody
    public Result<String> orderPaid(@RequestBody FeishuOrderPaidEvent event) {
        TemplateResult result = feishuOrderPaidEventHandlerTemplate.execute(event);
        return Result.newError(result.getCode(),result.getMsg());
    }

    /**
     * 通用下单工具
     * @param orderArg
     * @return
     */
    @RequestMapping(value="/commonOrder",method = RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.order.contacts.proxy.api.result.Result<Void> commonOrder(@RequestBody CreateCrmOrderArg orderArg) {
        return fsOrderServiceProxy.createCrmOrder(orderArg);
    }

    /**
     * 通用下单工具
     * @param fsEa
     * @param orderAmount
     * @param beginTime
     * @param endTime
     * @return
     */
    @RequestMapping(value="/buyConnector",method = RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector(@RequestParam String fsEa,
                                                                                        @RequestParam String orderAmount,
                                                                                        @RequestParam Long beginTime,
                                                                                        @RequestParam Long endTime) {
        return orderService.buyConnector(fsEa, orderAmount, beginTime, endTime);
    }

    /**
     * 飞书自动绑定企业解绑接口，只支持一对一对场景
     * @param displayId
     * @return
     */
    @RequestMapping(value="/feishuUnBindWithFs",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> feishuUnBindWithFs(@RequestParam String displayId) {
        return enterpriseBindService.feishuUnBindWithFs(displayId);
    }

    /**
     * 创建纷享员工接口
     * @param arg
     * @return
     */
    @RequestMapping(value="/createUser",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> createUser(@RequestBody FsEmpArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode(),FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
        log.info("ToolsController.createUser,result={}",result);
        return Result.newError(result.getCode(),result.getMsg());
    }

    /**
     * 更新纷享员工接口
     * @param arg
     * @return
     */
    @RequestMapping(value="/updateUser",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> updateUser(@RequestBody FsEmpArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.update(arg,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode(),FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
        log.info("ToolsController.updateUser,result={}",result);
        return Result.newError(result.getCode(),result.getMsg());
    }

    /**
     * 初始化，通过名称进行自动账号绑定
     * @param
     * @return
     */
//    @RequestMapping(value="/autoBindAccountBySameNme",method = RequestMethod.POST)
//    @ResponseBody
//    public Result<Void> autoBindAccountBySameNme(@RequestParam String outEa) {
//        return employeeBindService.refreshContactScopeDataCacheAsync(outEa);
//    }

    @RequestMapping(value="/addManagerRole",method = RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.order.contacts.proxy.api.result.Result<Void> addManagerRole(@RequestParam Integer ei, @RequestParam List<Integer> empIdList) {
        return fsEmployeeServiceProxy.addManagerRole(ei, empIdList);
    }

    @RequestMapping(value="/initEnterprisesInfoCache",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> initEnterprisesInfoCache() {
        return toolsService.initEnterprisesCache();
    }

    @RequestMapping(value="/initEnterpriseInfoCache",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> initEnterpriseInfoCache(@RequestParam String fsEa,
                                                @RequestParam(required = false) String outEa) {
        return toolsService.initEnterpriseCache(fsEa,outEa);
    }

    @RequestMapping(value="/clearEnterpriseInfoCache",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> clearEnterpriseInfoCache(@RequestParam String fsEa,
                                                 @RequestParam Integer type,
                                                 @RequestParam(required = false) String outEa,@RequestParam(required = false) String appId) {
        return toolsService.clearEnterpriseInfoCache(fsEa, outEa, type,appId);
    }

    /**
     * 飞书创建纷享企业情况
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEnterpriseOpen",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEnterpriseOpen(@RequestParam String displayId) {
        return toolsService.queryFsEnterpriseOpen(displayId);
    }

    /**
     * 飞书创建纷享人员情况
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEmployeeOpen",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEmployeeOpen(@RequestParam String displayId, @RequestParam String phone) {
        return toolsService.queryFsEmployeeOpen(displayId, phone);
    }

    /**
     * 查询企业的绑定类型
     * @param
     * @return
     */
    @RequestMapping(value = "/queryEnterpriseBindType",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryEnterpriseBindType(@RequestParam String fsEa,
                                                  @RequestParam(required = false) String outEa) {
        return toolsService.queryEnterpriseBindType(fsEa, outEa);
    }

    /**
     * 查询纷享人员当前的状态
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEmployeeStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEmployeeStatus(@RequestParam String dataCenterId, @RequestParam String phone) {
        return toolsService.queryFsEmployeeStatus(dataCenterId, phone);
    }

    /**
     * 迁移
     * @param
     * @return
     */
    @RequestMapping(value = "/pushCorpBindData2Cloud",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> pushCorpBindData2Cloud(@RequestParam String domain) {
        return toolsService.pushCorpBindData2Cloud(domain);
    }

    /**
     * 迁移
     * @param
     * @return
     */
    @RequestMapping(value = "/pushAppBindData2Cloud",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> pushAppBindData2Cloud(@RequestParam String domain) {
        return toolsService.pushAppBindData2Cloud(domain);
    }
//
//    /**
//     * 迁移
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/pushEnterpriseData2Cloud",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> pushEnterpriseData2Cloud(@RequestParam String fsEa, @RequestParam String domain) {
//        return toolsService.pushEnterpriseData2Cloud(fsEa, domain);
//    }

    /**
     * 迁移
     * @param
     * @return
     */
    @RequestMapping(value = "/updateEnterpriseDomain",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> updateEnterpriseDomain(@RequestParam String fsEa, @RequestParam String domain) {
        return toolsService.updateEnterpriseDomain(fsEa, domain);
    }

    @RequestMapping(value = "/deleteEnterpriseBindById",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> deleteEnterpriseBindById(@RequestParam String fsEa, @RequestParam String outEa, @RequestParam String domain) {
        return toolsService.deleteEnterpriseBindById(fsEa, outEa, domain);
    }

    @RequestMapping(value = "/deletePhoneBind",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> deletePhoneBind(@RequestParam String fsEa, @RequestParam String phone) {
        return toolsService.deletePhoneBind(fsEa, phone);
    }

    @RequestMapping(value = "/addServiceAuth",method =RequestMethod.POST)
    @ResponseBody
    public Result<Integer> addServiceAuth(@RequestBody FsServiceAuthInfo fsServiceAuthInfo) {
        return serviceAuthService.addServiceAuth(fsServiceAuthInfo);
    }
    @RequestMapping(value = "/brushData",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> brushData() {
        return toolsService.brushDataAppIdByFeishu();
    }

    @RequestMapping(value = "/createCrmCustomer2",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmCustomer2(@RequestBody CreateCustomerArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<String> result = fsOrderServiceProxy.createCustomer2(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmOrder2",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmOrder2(@RequestBody CreateCrmOrderArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder2(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmCustomer",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmCustomer(@RequestBody CreateCustomerArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCustomer(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmOrder",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmOrder(@RequestBody CreateCrmOrderArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/transferData",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> transferData(@RequestBody CreateCrmOrderArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder2(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/transferDb",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> transferDb(@RequestParam(value = "ea",required = false) String ea) {
        if(StringUtil.isNotEmpty(ea)){
            toolsManager.transfer(ea);
            return Result.newSuccess();
        }
       enterpriseBindManager.batchProcessEnterprises();

        return new Result<>();
    }




}