package com.facishare.open.feishu.syncapi.data;

import com.fxiaoke.crmrestapi.common.data.TypeHashMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ObjectData extends TypeHashMap<String, Object> implements ToStringFieldLimitableData {
    // 保护字段,不允许变更
    public static final Set<String> PROTECTED_FIELD = Sets.newHashSet("_id", "object_describe_api_name", "tenant_id");

    public static ObjectData convert(Map map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        ObjectData objectData = new ObjectData();
        objectData.putAll(map);
        return objectData;
    }

    public String getApiName() {
        return this.getString("object_describe_api_name");
    }

    public void putApiName(String apiName) {
        this.put("object_describe_api_name", apiName);
    }

    public String getId() {
        return this.getString("_id");
    }

    public void putId(String id) {
        this.put("_id", id);
    }

    public void safePutFrom(ObjectData from, String key) {
        if (from.containsKey(key)) {
            this.put(key, from.get(key));
        }
    }

    public List<String> getOwner() {
        Object ownerId = this.get("owner");
        if (ownerId == null) {
            return new ArrayList<>(0);
        }
        return (List<String>) ownerId;
    }

    public List<String> getOutOwner() {
        Object outOwnerId = this.get("out_owner");
        if (outOwnerId == null) {
            return new ArrayList<>(0);
        }
        return (List<String>) outOwnerId;
    }

    public void putOwner(List<String> ownerId) {
        this.put("owner", ownerId);
    }

    public List<String> getCreatedBy() {
        Object createdBy = this.get("created_by");
        if (createdBy == null) {
            return new ArrayList<>(0);
        }
        return (List<String>) createdBy;
    }

    public void putCreatedBy(List<String> createdBy) {
        this.put("created_by", createdBy);
    }

    public void putTenantId(String tenantId) {
        this.put("tenant_id", tenantId);
    }

    public void putOutTenantId(Long outTenantId) {
        this.put("out_tenant_id", outTenantId);
    }

    public String getName() {
        return this.getString("name");
    }

    public String getTenantId() {
        return this.getString("tenant_id");
    }

    public Long getVersion() {
        return this.getLong("version");
    }

    public String getLifeStatus() {
        return this.getString("life_status");
    }

    public String getLastModifiedBy() {
        Object lastModifiedBy = this.get("last_modified_by");
        if (lastModifiedBy == null) {
            return null;
        }
        if (lastModifiedBy instanceof List) {
            if (CollectionUtils.isEmpty((List) lastModifiedBy)) {//兼容空list的情况
                return null;
            }
            return ((List) lastModifiedBy).get(0).toString();
        }
        return lastModifiedBy.toString();
    }

    public String getRecordType() {
        return this.getString("record_type");
    }

    public String getMapValue(String apiName, String key) {
        Map map = getMap(apiName + "__r");
        if (map == null) {
            return null;
        }
        return (String) map.get(key);
    }

    public Object getReferName(String apiName) {
        return this.getString(apiName + "__r");
    }

    public void putLastModifiedBy(String lastModifyBy) {
        if (!StringUtils.isEmpty(lastModifyBy)) {
            this.put("last_modified_by", Lists.newArrayList(lastModifyBy));
        }
    }

    @Override
    public String toString() {
        //最多输出2000个字段
        return limitToString(2000);
    }

    @Override
    public String limitToString(int limitFieldsNum) {
        int maxFieldsFix = limitFieldsNum / 2;
        ToStringBuilder toStringBuilder = new ToStringBuilder(this, ToStringStyle.JSON_STYLE);
        //最多打印前后maxFields个字段
        int i = 0;
        for (Entry<String, Object> entry : this.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();
            if (i < maxFieldsFix || i > this.size() - maxFieldsFix + 1) {
                if (v instanceof CharSequence) {
                    //文本过长，截断
                    String s = v.toString();
                    if (s.length() > 1000) {
                        s = s.substring(0, 1000) + " skip...";
                    }
                    toStringBuilder.append(k, s);
                } else {
                    toStringBuilder.append(k, v);
                }
            } else if (i == maxFieldsFix) {
                //增加标记省略的字段数量
                toStringBuilder.append("skip fields", (this.size() - maxFieldsFix * 2));
            }
            i++;
        }
        return toStringBuilder.toString();
    }
}
