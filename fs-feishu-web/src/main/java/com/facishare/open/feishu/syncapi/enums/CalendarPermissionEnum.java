package com.facishare.open.feishu.syncapi.enums;

public enum CalendarPermissionEnum {
    PRIVATE("private", "私密"),
    SHOW_ONLY_FREE_BUSY("show_only_free_busy", "仅展示忙闲信息"),
    PUBLIC("public", "他人可查看日程详情");

    private final String code;
    private final String description;

    // 构造方法
    CalendarPermissionEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取code的方法
    public String getCode() {
        return code;
    }

    // 获取description的方法
    public String getDescription() {
        return description;
    }
}
