package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.mapper.SuperAdminDao;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.SuperAdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 管理工具类实现类
 * <AUTHOR>
 * @date 2021/11/01
 */
@Slf4j
@Service("superAdminService")
public class SuperAdminServiceImpl implements SuperAdminService {
    @Resource
    private SuperAdminDao superAdminDao;

    @Override
    public Result<List<Map<String, Object>>> superQuerySql(String sqlStr) {
        return new Result(superAdminDao.superQuerySql(sqlStr));
    }

    @Override
    public Result<Integer> superInsertSql(String sqlStr) {
        try {
            int result = superAdminDao.superInsertSql(sqlStr);
            return new Result(result);
        } catch (Exception e) {
            return new Result(-1,e.getMessage(),null);
        }
    }

    @Override
    public Result<Integer> superUpdateSql(String sqlStr) {
        try {
            int result = superAdminDao.superUpdateSql(sqlStr);
            return new Result(result);
        } catch (Exception e) {
            return new Result(-1,e.getMessage(),null);
        }
    }

    @Override
    public Result<Integer> superDeleteSql(String sqlStr) {
        try {
            int result = superAdminDao.superDeleteSql(sqlStr);
            return new Result(result);
        } catch (Exception e) {
            return new Result(-1,e.getMessage(),null);
        }
    }
}
