package com.facishare.open.feishu.web.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.DepartmentBindService;
import com.facishare.open.feishu.web.excel.BaseListener;
import com.facishare.open.feishu.web.excel.ImportExcelFile;
import com.facishare.open.feishu.web.excel.vo.DepartmentMappingVo;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.google.common.base.Joiner;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Getter
public class DepartmentDataMappingListener extends BaseListener<DepartmentMappingVo> {

    private final ImportExcelFile.Result importResult;
    private String fsEa;
    private String outEa;
    private Integer fsUserId;
    private DepartmentBindService departmentBindService;
    private String crmAppId;
    private String lang;
    private String tenantId;
    private I18NStringManager i18NStringManager;

    public DepartmentDataMappingListener(String fsEa,
                                         String outEa,
                                         Integer fsUserId,
                                         DepartmentBindService departmentBindService,
                                         String crmAppId,
                                         String lang,
                                         String tenantId,
                                         I18NStringManager i18NStringManager) {
        importResult = new ImportExcelFile.Result();
        this.fsEa = fsEa;
        this.outEa = outEa;
        this.fsUserId = fsUserId;
        this.departmentBindService = departmentBindService;
        this.crmAppId = crmAppId;
        this.lang = lang;
        this.tenantId = tenantId;
        this.i18NStringManager = i18NStringManager;
    }

    @Override
    public void invoke(DepartmentMappingVo data, AnalysisContext context) {
        super.invoke(data, context);
        log.info("DepartmentDataMappingListener.invoke,data={}", data);
        log.info("DepartmentDataMappingListener.invoke,outEa={}", outEa);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();
        Result<OuterOaDepartmentBindEntity> departmentMapping = departmentBindService.queryDepData(ChannelEnum.feishu,
                fsEa,
                data.getFsDepId()+"",
                outEa,
                data.getOutDepId());
        log.info("DepartmentDataMappingListener.invoke,departmentMapping={}", departmentMapping);
        if(departmentMapping.getData()==null) {
            DepartmentBindEntity entity = new DepartmentBindEntity();
            entity.setChannel(ChannelEnum.feishu);
            entity.setFsEa(fsEa);
            entity.setFsDepId(data.getFsDepId()+"");
            entity.setOutEa(outEa);
            entity.setOutDepId(data.getOutDepId());
            entity.setDepCode(data.getOutDepId());
            entity.setBindStatus(BindStatusEnum.normal);
            entity.setBindType(BindTypeEnum.manual);

            log.info("DepartmentDataMappingListener.invoke,departmentBindEntity={}", entity);
            try {
                Result<Integer> result = departmentBindService.insertDepData(entity);
                log.info("DepartmentDataMappingListener.invoke,result={}", result);
                if(result.isSuccess() && result.getData() > 0) {
                    importResult.incrInsert(1);
                } else {
                    importResult.addImportError(rowNo,result.getMsg());
                }
            } catch (Exception e) {
                log.info("DepartmentDataMappingListener.invoke,exception={}", e.getMessage(),e);
                importResult.addInvokeExceptionRow(rowNo,e.getMessage());
            }
        } else {
            importResult.incrUpdate(1);
        }
    }
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get(I18NStringEnum.s132, lang, tenantId));
        printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s126, lang, tenantId), importResult.getInvokedNum(),
                        importResult.getInsertNum(),
                        importResult.getUpdateNum()))
                .append("\n");

        printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s127, lang, tenantId), importResult.getImportErrorRows().size()))
                .append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s128, lang, tenantId), Joiner.on(",").join(v), k))
                    .append("\n");
        });
        printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s129, lang, tenantId), importResult.getInvokeExceptionRows().size()))
                .append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s128, lang, tenantId), Joiner.on(",").join(v), k))
                    .append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
