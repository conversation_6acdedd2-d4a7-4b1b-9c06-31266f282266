<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <import resource="spring-cms.xml"/>
    <context:annotation-config/>
    <task:annotation-driven executor="taskExecutor"/>

    <context:component-scan base-package="com.facishare.open.feishu,com.facishare.open.order.contacts.proxy,com.facishare.open.outer.oa"/>

    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
    <import resource="dubbo-config.xml"/>
    <import resource="dubbo-consumer.xml"/>
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>
    <import resource="classpath:/spring/spring-mq.xml"/>
    <import resource="classpath:spring/outer-oa-connector-i18n.xml"/>
        <import resource="classpath:spring/db-proxy-spring.xml"/>
        <import resource="classpath:spring/db-application.xml"/>

    <!--okHttp-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-feishu-config"/>

    <!--redis配置-->
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-feishu-config"/>
    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>

    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <bean id="serviceAspect" class="com.facishare.open.order.contacts.proxy.api.aop.ServiceAspect"/>
    <bean id="controllerI18NAspect" class="com.facishare.open.feishu.web.aop.ControllerI18NAspect"/>
    <aop:config>
        <aop:aspect ref="controllerI18NAspect">
            <aop:pointcut id="i18NAspect" expression="execution(* com.facishare.open.feishu.web.controller..*.*(..))"/>
            <aop:around method="around" pointcut-ref="i18NAspect"/>
        </aop:aspect>
        <aop:aspect ref="serviceAspect">
            <aop:pointcut id="pointcut1" expression="(execution(* com.facishare.open.feishu.web.controller..*.*(..)))
            || (execution(* com.facishare.open.feishu.web.handler.*.*(..)))"/>
            <aop:around method="around" pointcut-ref="pointcut1"/>
        </aop:aspect>
    </aop:config>

    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="104857600"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>

<!--    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiServiceProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>-->
<!--    </bean>-->
<!--    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">-->
<!--        <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
<!--    </bean>-->
<!--    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">-->
<!--        <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
<!--    </bean>-->

<!--    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>-->
<!--    </bean>-->

<!--    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>-->
<!--    </bean>-->

    <!--    feishu-sync-provider applicationContext.xml-->
<!--    <context:annotation-config/>-->
<!--    <task:annotation-driven executor="taskExecutor"/>-->
<!--    <context:component-scan base-package="com.facishare.open.feishu.sync,com.facishare.open.order.contacts.proxy.api,com.facishare.open.outer.oa"/>-->
<!--    <import resource="spring-cms.xml"/>-->
    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
<!--    <import resource="dubbo-config.xml"/>-->
    <import resource="dubbo-provider.xml"/>
<!--    <import resource="dubbo-consumer.xml"/>-->
    <import resource="spring-db.xml"/>
    <import resource="all-rest-api.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
    <!--    <import resource="spring-job.xml"/>-->
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
<!--    <import resource="classpath:/spring/spring-mq.xml"/>-->
    <!-- 引入license-->
    <import resource="classpath:spring/license-client.xml"/>
    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>
    <import resource="classpath:spring/fs-warehouse-rest-client.xml"/>
    <!--    <import resource="classpath:otherrest/otherrest.xml"/>-->
    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <import resource="classpath:spring/mongo-store.xml"/>
    <import resource="classpath:spring/spring-job.xml"/>

<!--    &lt;!&ndash;redis配置&ndash;&gt;-->
<!--    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"-->
<!--          p:configName="fs-feishu-config"/>-->
<!--    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">-->
<!--        <property name="jedisCmd" ref="publishRedis"/>-->
<!--    </bean>-->

<!--    &lt;!&ndash;okHttp&ndash;&gt;-->
<!--    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"-->
<!--          p:configName="fs-feishu-config"/>-->


<!--    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">-->
<!--        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>-->
<!--    </bean>-->

    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>

    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>
    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 蜂眼监控 -->
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
<!--    &lt;!&ndash;    日志类&ndash;&gt;-->
    <bean class="com.facishare.open.order.contacts.proxy.api.utils.LogUtils"/>
    <!--    <bean id="serviceAspect" class="com.facishare.open.order.contacts.proxy.api.aop.ServiceAspect"/>-->
    <aop:config>
        <aop:pointcut id="pointcut1" expression="(execution(* com.facishare.open.feishu.sync.service.impl.*.*(..)))
            || (execution(* com.facishare.open.feishu.sync.manager.*.*(..)))"/>
        <aop:pointcut id="CrmRateLimiter"
                      expression="(execution(* com.facishare.converter.EIEAConverter.*(..)) or
            execution(* com.facishare.uc.api.service.EnterpriseEditionService.*(..)) or
            execution(* com.facishare.warehouse.api.service.NFileStorageService.*(..)))"/>
        <aop:aspect ref="crmRateLimiterAspect">
            <aop:around method="around" pointcut-ref="CrmRateLimiter"/>
        </aop:aspect>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:around method="profile" pointcut-ref="pointcut1"/>
        </aop:aspect>
        <aop:aspect ref="serviceAspect">
            <aop:around method="around" pointcut-ref="pointcut1"/>
        </aop:aspect>
    </aop:config>


<!--    &lt;!&ndash; 异步线程池 &ndash;&gt;-->
<!--    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">-->
<!--        &lt;!&ndash; 核心线程数  &ndash;&gt;-->
<!--        <property name="corePoolSize" value="1"/>-->
<!--        &lt;!&ndash; 最大线程数 &ndash;&gt;-->
<!--        <property name="maxPoolSize" value="32"/>-->
<!--        &lt;!&ndash; 队列最大长度 >=mainExecutor.maxSize &ndash;&gt;-->
<!--        <property name="queueCapacity" value="1800"/>-->
<!--        &lt;!&ndash; 线程池维护线程所允许的空闲时间 &ndash;&gt;-->
<!--        <property name="keepAliveSeconds" value="300"/>-->
<!--        &lt;!&ndash;允许核心线程超时销毁&ndash;&gt;-->
<!--        <property name="allowCoreThreadTimeOut" value="true"/>-->
<!--        &lt;!&ndash; 线程池对拒绝任务(无线程可用)的处理策略 &ndash;&gt;-->
<!--        <property name="rejectedExecutionHandler">-->
<!--            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>-->
<!--        </property>-->
<!--    </bean>-->

    <!--redisson-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-feishu-config"/>
    </bean>
    <!--    feishu-sync-provider applicationContext.xml-->
    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>
</beans>