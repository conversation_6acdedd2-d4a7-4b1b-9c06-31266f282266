<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
            http://www.springframework.org/schema/context
            http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.facishare.open.ding,com.facishare.open.outer.oa.connector.i18n"/>

    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor" c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="fs-open-dingtalk-all"/>

<!--    <import resource="dubbo-consumer.xml"/>-->
<!--    <import resource="spring-aop.xml"/>-->

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
<!--    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"-->
<!--          p:configName="fs-open-call-center-cms-all"/>-->



    <!--fs-dingtalk-provider applicationContext.xml -->
    <import resource="dubbo-consumer.xml"/>
    <import resource="dubbo-provider.xml"/>
    <import resource="spring-jdbc.xml"/>
    <import resource="spring-aop.xml"/>
    <import resource="spring-mongo.xml"/>
    <import resource="spring-redis.xml"/>
    <import resource="classpath*:crmrest/crmrest.xml"/>
    <import resource="classpath:META-INF/fs-spring-rest-plugin.xml"/>

    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>
    <!--fs-orgainzation-adapter-api -->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>
    <!--  新增连接器需要单独配置-->
    <import resource="classpath:fs-paas-dao-support.xml"/>

    <!-- 分布式锁配置 -->
    <!--    <bean id="lockUtils" class="com.facishare.open.ding.provider.utils.LockUtils">-->
    <!--        <constructor-arg name="zkUrl" value="${lock.zookeeper.url}" />-->
    <!--        <constructor-arg name="basePath" value="/conf/lock/dingtalk" />-->
    <!--    </bean>-->

    <!-- okHttp3 monitor -->
    <bean id="httpClientSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-open-platform-okhttp3"/>

<!--    &lt;!&ndash; JVM，logback 监控上报 &ndash;&gt;-->
<!--    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>-->
    <!--fs-dingtalk-provider applicationContext.xml -->


    <!--fs-dingtalk-cloud applicationContext.xml -->
<!--    <import resource="dubbo-consumer.xml"/>-->
<!--    <import resource="dubbo-provider.xml"/>-->
<!--    <import resource="spring-jdbc.xml"/>-->
<!--    <import resource="spring-aop.xml"/>-->
    <import resource="classpath*:crmrest/crmrest.xml"/>

    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="spring-job.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
<!--    <import resource="spring-redis.xml"/>-->
    <import resource="classpath:META-INF/fs-spring-dubbo-plugin.xml"/>
    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
<!--    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>-->
    <!--fs-orgainzation-adapter-api -->
<!--    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>-->
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
<!--    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>-->
    <!-- 引入license-->
    <import resource="classpath:spring/license-client.xml"/>
    <import resource="classpath:spring/eservice-rest.xml"/>
    <import resource="classpath:otherrest/otherrest.xml"/>
<!--    <import resource="classpath:META-INF/fs-spring-rest-plugin.xml"/>-->

    <!-- 分布式锁配置 -->
    <!--    <bean id="lockUtils" class="com.facishare.open.ding.provider.utils.LockUtils">-->
    <!--        <constructor-arg name="zkUrl" value="${lock.zookeeper.url}" />-->
    <!--        <constructor-arg name="basePath" value="/conf/lock/dingtalk" />-->
    <!--    </bean>-->


<!--    &lt;!&ndash; JVM，logback 监控上报 &ndash;&gt;-->
<!--    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>-->

    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-open-call-center-cms-all"/>

<!--    <bean id="passObjectDataMqConsumer"-->
<!--          class="com.facishare.open.ding.cloud.paas.PaasObjectDataMqConsumer"-->
<!--          init-method="init"-->
<!--          p:rocketMQConsumerConfigName="fs-dingtalk-sync-data-consumer"/>-->

    <!--    <bean id="fsEventListener" name="fsEventListener" class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
    <!--        <constructor-arg index="0" value="fs-open-dingtalk-all"></constructor-arg>-->
    <!--        <constructor-arg index="1" value="tenant.name.server"></constructor-arg>-->
    <!--        <constructor-arg index="2" value="tenant.consumer.group"></constructor-arg>-->
    <!--        <constructor-arg index="3" value="tenant.consume.topic"></constructor-arg>-->
    <!--        <constructor-arg index="4" ref="enterpriseEventListener"></constructor-arg>-->
    <!--    </bean>-->

    <bean id="fsEventListener" class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-open-dingtalk-all"/>
        <constructor-arg name="sectionNames" value="CREATE_ENTERPRISE_SECTION"/>
        <constructor-arg name="messageListener" ref="enterpriseEventListener"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <!--fs-dingtalk-cloud applicationContext.xml -->


    <!--迁移到新库-->
    <import resource="classpath:spring/db-application.xml"/>
    <import resource="classpath:spring/db-transfer.xml"/>
<!--    <import resource="classpath:spring/db-proxy-spring.xml"/>-->
    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>
</beans>
