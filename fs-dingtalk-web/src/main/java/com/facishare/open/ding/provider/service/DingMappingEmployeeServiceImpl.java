package com.facishare.open.ding.provider.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingMappingEmployeeService;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service("dingMappingEmployeeServiceImpl")
@Slf4j
public class DingMappingEmployeeServiceImpl implements DingMappingEmployeeService {
    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    @Autowired
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;

    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public DingMappingEmployeeResult findMappingByEmployeeId(Integer ei, Integer fsEmployeeId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String fsEmpId = String.valueOf(fsEmployeeId);

        // 查询绑定关系
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);

        OuterOaEmployeeBindEntity employeeBind = outerOaEmployeeBindMapper.selectOne(wrapper);
        if (Objects.isNull(employeeBind)) {
            log.info("未找到对应的钉钉员工绑定关系: ei={}, fsEmployeeId={}, appId={}", ei, fsEmployeeId, appId);
            return null;
        }

        // 查询员工信息
        LambdaQueryWrapper<OuterOaEmployeeDataEntity> userWrapper = Wrappers.lambdaQuery();
        userWrapper.eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeDataEntity::getOutEa, employeeBind.getOutEa())
                .eq(OuterOaEmployeeDataEntity::getOutUserId, employeeBind.getOutEmpId());
        if (appId != null) {
            userWrapper.eq(OuterOaEmployeeDataEntity::getAppId, appId);
        }
        OuterOaEmployeeDataEntity userInfo = outerOaEmployeeDataMapper.selectOne(userWrapper);
        
        if (Objects.isNull(userInfo)) {
            log.warn("未找到对应的钉钉员工信息: ei={}, fsEmployeeId={}, dingEmployeeId={}, appId={}", ei, fsEmployeeId, employeeBind.getOutEmpId(), appId);
            return null;
        }

        // 转换为结果对象
        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
        result.setEi(ei);
        result.setEmployeeId(fsEmployeeId);
        result.setDingEmployeeId(employeeBind.getOutEmpId());
        result.setBindStatus(employeeBind.getBindStatus().ordinal());
        result.setCreateTime(new Date(employeeBind.getCreateTime()));
        result.setUpdateTime(new Date(employeeBind.getUpdateTime()));

        // 设置员工详细信息
        DingTalkEmployeeObject employeeInfo = userInfo.getOutUserInfo().toJavaObject(DingTalkEmployeeObject.class);
        if (Objects.nonNull(employeeInfo)) {
            result.setDingEmployeeStatus(employeeInfo.getStatus());
            result.setDingEmployeeName(employeeInfo.getName());
            result.setDingEmployeePhone(employeeInfo.getPhone());
            result.setDingUnionId(employeeInfo.getUnionId());
            result.setDingDeptId(employeeInfo.getDeptId());
            result.setDingDeptName(employeeInfo.getDeptName());
        }

        return result;
    }

    @Override
    public DingMappingEmployeeResult findMappingByDingUserId(Integer ei, String dingUserId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);

        // 查询绑定关系
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getOutEmpId, dingUserId)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);

        OuterOaEmployeeBindEntity employeeBind = outerOaEmployeeBindMapper.selectOne(wrapper);
        if (Objects.isNull(employeeBind)) {
            log.info("未找到对应的钉钉员工绑定关系: ei={}, dingUserId={}, appId={}", ei, dingUserId, appId);
            return null;
        }

        // 查询员工信息
        LambdaQueryWrapper<OuterOaEmployeeDataEntity> userWrapper = Wrappers.lambdaQuery();
        userWrapper.eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeDataEntity::getOutEa, employeeBind.getOutEa())
                .eq(OuterOaEmployeeDataEntity::getOutUserId, dingUserId);
        if (appId != null) {
            userWrapper.eq(OuterOaEmployeeDataEntity::getAppId, appId);
        }
        OuterOaEmployeeDataEntity userInfo = outerOaEmployeeDataMapper.selectOne(userWrapper);

        if (Objects.isNull(userInfo)) {
            log.warn("未找到对应的钉钉员工信息: ei={}, dingUserId={}, appId={}", ei, dingUserId, appId);
            return null;
        }

        // 转换为结果对象
        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
        result.setEi(ei);
        result.setEmployeeId(Integer.valueOf(employeeBind.getFsEmpId()));
        result.setDingEmployeeId(dingUserId);
        result.setBindStatus(employeeBind.getBindStatus().ordinal());
        result.setCreateTime(new Date(employeeBind.getCreateTime()));
        result.setUpdateTime(new Date(employeeBind.getUpdateTime()));

        // 设置员工详细信息
        DingTalkEmployeeObject employeeInfo = userInfo.getOutUserInfo().toJavaObject(DingTalkEmployeeObject.class);
        if (Objects.nonNull(employeeInfo)) {
            result.setDingEmployeeStatus(employeeInfo.getStatus());
            result.setDingEmployeeName(employeeInfo.getName());
            result.setDingEmployeePhone(employeeInfo.getPhone());
            result.setDingUnionId(employeeInfo.getUnionId());
            result.setDingDeptId(employeeInfo.getDeptId());
            result.setDingDeptName(employeeInfo.getDeptName());
        }

        return result;
    }
}
