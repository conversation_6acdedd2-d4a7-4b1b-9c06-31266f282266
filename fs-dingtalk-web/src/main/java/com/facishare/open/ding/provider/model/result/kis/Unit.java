package com.facishare.open.ding.provider.model.result.kis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * KIS基础计量单位
 * Created by system on 2018/5/18.
 */
@Data
public class Unit {

    /** 计量单位内码ID **/
    @SerializedName(value = "UnitID")
    private Integer unitId;

    /** 计量单位编码 **/
    @SerializedName(value = "Number")
    private String number;

    /** 计量单位名称 **/
    @SerializedName(value = "Name")
    private String name;

}
