package com.facishare.open.ding.provider.enums;

import com.facishare.open.ding.api.enums.CrmApiNameEnum;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @<NAME_EMAIL>
 * @ClassName: ConfigService
 * @Description: 配置类
 * @datetime 2019/3/1 10:29
 * @Version 1.0
 */
@Slf4j
public class ConfigService {

    private static IConfigFactory factory = ConfigFactory.getInstance();

    private static String EI_FIELD_NAME = "erp.stock.ei";

    private static Set<String> EI_SETS = new HashSet<>();

    private static final String ERP_STOCK_APINAME = CrmApiNameEnum.ErpStockObj.getApiName();
    private static final String STOCK_APINAME = CrmApiNameEnum.StockObj.getApiName();
    private static final String ERP_WAREHOUSE_APINAME = CrmApiNameEnum.ErpWarehouseObj.getApiName();
    private static final String WAREHOUSE_APINAME = CrmApiNameEnum.WarehouseObj.getApiName();

    static{
        factory.getConfig("fs-k3cloud-provider", config -> {
            List<String> eiLst = Arrays.asList(config.get(EI_FIELD_NAME, "").split(";"));
            EI_SETS.clear();
            eiLst.forEach(ei ->{
                EI_SETS.add(ei);
            });
        });
    }

    /**
     * 获取库存apiName
     * @param ei
     * @return
     */
    public static String getApiName(Integer ei) {
        if(EI_SETS.contains(ei.toString())) {
            return ERP_STOCK_APINAME;
        }else {
            return STOCK_APINAME;
        }
    }

    /**
     * 获取仓库apiName
     * @param ei
     * @return
     */
    public static String getWareHouseApiName(Integer ei) {
        if(EI_SETS.contains(ei.toString())) {
            return ERP_WAREHOUSE_APINAME;
        }else {
            return WAREHOUSE_APINAME;
        }
    }

    /**
     * 禁止外部实例化
     */
    private ConfigService(){
        throw new AssertionError();
    }

}
