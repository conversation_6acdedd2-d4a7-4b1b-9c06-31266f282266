package com.facishare.open.ding.api.enums;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/26 10:58
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public enum SyncLogStatusEnum {

    /** 根任务 */
    ROOT(-1),

    /** 全部 **/
    ALL(0),

    /** 错误未恢复 **/
    NO_RECOVER(1),

    /** 错误已恢复 **/
    RECOVERED(2),

    /** 成功 **/
    SUCCESS(3);

    private int status;

    SyncLogStatusEnum(int status) {
       this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public static boolean isInvalid(Integer status) {
        if (status != null) {
            for (SyncLogStatusEnum statusEnum : SyncLogStatusEnum.values()) {
                if (statusEnum.getStatus() == status) {
                    return false;
                }
            }
        }
        return true;
    }

}
