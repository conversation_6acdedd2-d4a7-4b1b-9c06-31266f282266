package com.facishare.open.ding.provider.dingding;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-19 14:30
 */
public interface DingUrl {
    String GET = "GET";

    String POST = "POST";

    //获取token
    String GET_TOKEN_URL = "https://oapi.dingtalk.com/gettoken";

    //注册回调接口
    String REGISTER_URL = "https://oapi.dingtalk.com/call_back/register_call_back";

    //查询部门列表
    String DEPT_LIST = "https://oapi.dingtalk.com/department/list";

    //查询部门下用户列表
    String USER_LIST = "https://oapi.dingtalk.com/user/listbypage";

    //查询用户详情
    String GET_USER = "https://oapi.dingtalk.com/user/get";

    String CORP_MESSAGE = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";
    String SCOPE_URL = "https://oapi.dingtalk.com/auth/scopes";
    String DEPT_DETAIL = "https://oapi.dingtalk.com/department/get";
    String GET_USER_DETAIL = "https://oapi.dingtalk.com/user/get";
    String GET_DEPT_DETAIL = "https://oapi.dingtalk.com/department/get";
    String LIST_PARENT_DEPTS = "https://oapi.dingtalk.com/department/list_parent_depts_by_dept";
    String LIST_SMART_EMPLOYEES = "https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/list";
}
