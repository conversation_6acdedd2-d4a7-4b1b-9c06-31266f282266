package com.facishare.open.ding.common.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/3 10:05
 * @Version 1.0
 */
@Data
public class EmployeeDingVo  implements Serializable {
    private String unionid;
    private String userid;
    private String name;
    private String mobile;
    private String email;
    private List<Long> department;
    private Long mainDept;
    @JSONField(serialize = false)
    private String title;//职位  以前的接口是position
    private String avatarUrl;//unionid获取的头像 地址filename是这个，通讯录详情的是这个avatar
    private Boolean isAdmin=false;//bizdata标识管理员
    private Boolean admin=false;//enterpris标识管理员
}
