package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;

/**
 * <AUTHOR>
 * @Date 2021/5/23 15:27 http代理
 * @Version 1.0
 */
public interface DingProxyService {
    String getUserId(String corpId,String unionId,String suiteId);
    EmployeeDingVo getUserInfo(String corpId,String userId,String suiteId);
    Result<Integer> updateSql(String sql);
}
