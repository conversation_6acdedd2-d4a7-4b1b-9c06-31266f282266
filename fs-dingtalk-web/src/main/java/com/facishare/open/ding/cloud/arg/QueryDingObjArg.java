package com.facishare.open.ding.cloud.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/18 10:37
 * @Version 1.0
 */
@Data
public class QueryDingObjArg implements Serializable {
    private String cursor;//分页游标
    private Long page_size;//分页大小
    private String query_dsl;
    @Data
    public static class QueryDslArg implements Serializable{
        private String logicType;
        private List<FilterArg> queryObjectList;
    }

    @Data
    public static class FilterArg implements Serializable{
        private String fieldId;
        private String filterType;
        private Object value;
    }
}
