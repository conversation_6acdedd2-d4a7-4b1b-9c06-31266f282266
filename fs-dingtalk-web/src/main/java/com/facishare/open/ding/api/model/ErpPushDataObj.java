package com.facishare.open.ding.api.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/17 0:19
 * @Version 1.0
 */
@Data
public class ErpPushDataObj implements Serializable {
    /**
     * {
     * 	"objAPIName": "crm_customer_personal",
     * 	"masterFieldVal": {
     * 		"creator_userid": "创建人",
     * 		"customer_id": "客户ID",
     * 		"customer_name": "客户姓名",
     * 		"customer_follow_up_status": "客户跟进状态",
     * 		"address": "地址",
     * 		"customer_phone": "电话",
     * 		"email": "邮箱",
     * 		"remark": "备注"
     *        },
     * 	"detailFieldVals": {}
     * }
     */
    private String objAPIName;
    private Map<String,Object> masterFieldVal;
    private Map<String,Object> detailFieldVals;
}
