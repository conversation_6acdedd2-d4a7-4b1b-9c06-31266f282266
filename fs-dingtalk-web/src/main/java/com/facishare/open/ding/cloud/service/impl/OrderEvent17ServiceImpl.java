package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.arg.SendTextNoticeArg;
import com.facishare.open.ding.api.enums.BindType;
import com.facishare.open.ding.api.enums.BizServiceEnum;
import com.facishare.open.ding.api.enums.CloudDataTypeEnum;
import com.facishare.open.ding.api.enums.SourceTypeEnum;
import com.facishare.open.ding.api.model.*;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.service.cloud.CloudNotificationService;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.api.service.cloud.DingRefuseDataService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.result.CreateFsCustomerResult;
import com.facishare.open.ding.cloud.arg.CreateCrmOrderArg;
import com.facishare.open.ding.cloud.arg.CreateCustomerArg;
import com.facishare.open.ding.cloud.arg.WaitingTenantCreateArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.dao.OpenSyncBizDataDao;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.CrmManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.manager.OAConnectorOpenDataManager;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.ding.cloud.result.SaveOrderResult;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.cloud.service.api.DingOrderService;
import com.facishare.open.ding.cloud.template.outer.event.order.DingTalkOrderPaidEventHandlerTemplate;
import com.facishare.open.ding.cloud.utils.PinyinUtils;
import com.facishare.open.ding.cloud.utils.RedisLockUtils;
import com.facishare.open.ding.cloud.utils.SpringUtils;
import com.facishare.open.ding.cloud.utils.ValidNameUtils;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.restful.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/7 10:41 下单事件
 * @Version 1.0
 */
@Service("orderEvent17ServiceImpl")
@Slf4j
// IgnoreI18nFile
public class OrderEvent17ServiceImpl implements DingEventService, DingOrderService {

    @Autowired
    private CrmManager crmManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private CloudEmpService cloudEmpService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CloudOrderService cloudOrderService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private OpenSyncBizDataDao openSyncBizDataDao;
    public Map<Integer, DingEventService> serviceMap = Maps.newHashMap();
    @Autowired
    private DingRefuseDataService dingRefuseDataService;
    private static String WAITING_TENANT_ACCOMPLISH_EVENT="WAITING_TENANT_ACCOMPLISH_EVENT_%s";
    public static Long EXPIRE_TIME_SECONDS=24*60*60l;

    @Autowired
    private SpringUtils springUtils;
    private Integer MAX_PEOPLE = 999999;
    private String upgrade_standPro = "%s__UPGRADE_STANDARDPRO";
    private String upgrade_strengthen = "%s__UPGRADE_STRENGTHEN";

    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Autowired
    private CloudNotificationService cloudNotificationService;
    @Autowired
    private DingTalkOrderPaidEventHandlerTemplate dingTalkOrderPaidEventHandlerTemplate;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @PostConstruct
    public void init() {
        for (BizServiceEnum bizServiceEnum : BizServiceEnum.values()) {
            DingEventService service = (DingEventService) springUtils.getApplicationContext().getBean(bizServiceEnum.getBeanName());
            serviceMap.put(bizServiceEnum.getBizType(), service);
        }
    }


    private static final String ENTERPRISE_PREFIX = "dt";

    public static final String NEW_TYPE = "BUY";
    public static final String RENEW = "RENEW";
    public static final String UPGRADE = "UPGRADE";
    public static final String RENEW_UPGRADE = "RENEW_UPGRADE";
    public static final String RENEW_DEGRADE = "RENEW_DEGRADE";
    public static final String DT_MOCK_UPGRADE = "DT_MOCK_UPGRADE";//虚拟钉钉规格，对应纷享升级产品
    public static String ORDER_FORMAT = "DING_ORDER_%S";

    @Override
    public void executeEvent(HighBizDataDo eventData) {
        StopWatch stopWatch = StopWatch.create("free execute");
        OrderModel orderModel = JSONObject.parseObject(eventData.getBizData(), new TypeReference<OrderModel>() {
        });
        log.info("OrderEvent17ServiceImpl.executeEvent,orderModel={}", orderModel);
        log.info("event process :{}", orderModel.getOrderId());
        Integer count = cloudOrderService.queryCount(orderModel.getOrderId(), null);
        log.info("OrderEvent17ServiceImpl.executeEvent,count={}", count);
        if (count > 0) return;

        //添加分布式锁
        if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format(ORDER_FORMAT, orderModel.getOrderId()), orderModel.getCorpId(), 15)) {
            TemplateResult result = dingTalkOrderPaidEventHandlerTemplate.execute(orderModel);
            log.info("OrderEvent17ServiceImpl,execute,result={}",result);
//            switch (orderModel.getOrderType()) {
//                case NEW_TYPE:
//                    boolean b = newOrder(orderModel);
//                    if(!b){
//                        return;
//                    }
//                    break;
//                case RENEW:
//                    renewOrder(orderModel);
//                    break;
//                case UPGRADE:
//                    upgradeOrder(orderModel);
//                    break;
//                default:
//                    renewOrder(orderModel);
//            }
//            //保存订单
//            Integer orderCount = cloudOrderService.addOrder(orderModel);
//            log.info("order event17 orderModel:{},count:{}", orderModel, orderCount);
//            //避免前面高级事件查询的时候，漏掉bizType=4的事件，这边添加处理，处理完订单后，主动触发bizType=4
//            //个人组织的不同步组织架构，因为人员所属部门读取不到
//
//            HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(4, eventData.getCorpId(), orderModel.getSuiteId());
//            if(ObjectUtils.isNotEmpty(bizDataDo)){
//                serviceMap.get(bizDataDo.getBizType()).executeEvent(bizDataDo);
//            }
//            log.info("orderEvent fix bizType4 data:{}", bizDataDo);
            stopWatch.lap("createOrder");
            stopWatch.log();
        }
    }

    //新购订单
    private boolean newOrder(OrderModel orderModel) {
        log.info("newOrder,orderModel={}",orderModel);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId());
        final String appId = appParams.getAppId();
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(orderModel.getCorpId(), appId);
        log.info("newOrder,corpResult={}",corpResult);
        if (ObjectUtils.isEmpty(corpResult.getData())) {
            //未绑定企业同步订单
            return originCreateEnter(orderModel);
        } else {
            //已经绑定了企业同步订单
            existsEACreateOrder(orderModel, corpResult.getData().get(0));
        }
        return true;
    }

    public boolean originCreateEnter(OrderModel orderModel) {
        //crm的试用订单配额为全公司的配额
        String ea="";
        if (orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {
            if (("TRYOUT".equals(orderModel.getOrderChargeType()))) {
                //试用订单提供的人数是设置成企业人数
//                Integer empCount = dingManager.getEmpCount(orderModel.getCorpId(), orderModel.getSuiteId());
//                orderModel.setSubQuantity(empCount);
                //默认为 10000
                orderModel.setSubQuantity(ConfigCenter.createCrmAccount);
            }
            Map<String, Object> objectMap = initEnterPrise(orderModel);
            if(!Boolean.valueOf(objectMap.get("hasContinue").toString())){
                return false;
            }
            ea=objectMap.get("ea").toString();
        } else {
            List<OrderModel> modelList = splitAppOrder(orderModel, null, null);
            //非CRM应用的需要创建企成功后，同步附属产品
            Map<String, Object> objectMap = initEnterPrise(modelList.get(0));//先同步crm订单
            if(!Boolean.valueOf(objectMap.get("hasContinue").toString())){
                return false;
            }
             ea = objectMap.get("ea").toString();
            //继续同步附属产品
            syncAttachProduct(orderModel, ea);
        }
        //保存订单
        orderModel.setEa(ea);
        log.info("originCreateEnter,addOrder,orderModel={}",orderModel);
        Integer orderCount = cloudOrderService.addOrder(orderModel);
        log.info("originCreateEnter,addOrder,orderCount={}",orderCount);
        return false;//后续订单保存都在监听企业创建完成MQ的后续动作
    }

    public Long calculateFee(OrderModel orderModel, NeighborProductQuantity neighborProductQuantity) {
        //统一靠齐钉钉费用，以分计算
        if ("TRYOUT".equals(orderModel.getOrderChargeType()))
            return 0l;
        if (neighborProductQuantity.getChargeChannel().equals(1)) {
            return orderModel.getPayFee();
        } else if (neighborProductQuantity.getChargeChannel().equals(0)) {
            return neighborProductQuantity.getPayFee();
        } else {
            return orderModel.getPayFee() - neighborProductQuantity.getPayFee();
        }
    }


    private NeighborProductQuantity crmAccount(String itemCode, String crmProductId) {
        List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get(itemCode);
        log.info("neighborProductQuantities crmAccount:{}", neighborProductQuantities);
        List<NeighborProductQuantity> productQuantities = neighborProductQuantities.stream().filter(item -> item.getCrmProduct().equals(crmProductId)).collect(Collectors.toList());
        return productQuantities.get(0);
    }


    public void existsEACreateOrder(OrderModel orderModel, DingCorpMappingVo corpMappingVo) {
        log.info("existsEACreateOrder,orderModel={},corpMappingVo={}",orderModel,corpMappingVo);
        String enterPriseAccount = corpMappingVo.getEa();
        //可能会继续推送试用的订单
        if (orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {
            if (("TRYOUT".equals(orderModel.getOrderChargeType()))) {
                //试用订单提供的人数是设置成企业人数
//                Integer empCount = dingManager.getEmpCount(orderModel.getCorpId(), orderModel.getSuiteId());
//                orderModel.setSubQuantity(empCount);
                //默认为 10000
                orderModel.setSubQuantity(ConfigCenter.createCrmAccount);
            }
        }
        //有些产品在钉钉是附属产品，不需要拆单
        if (ConfigCenter.STRAIGHT_SYNC_PRODUCTS.contains(orderModel.getItemCode())) {
            syncAttachProduct(orderModel, corpMappingVo.getEa());
            return;
        }
        Map<String, Object> refuseMap = refuseOrder(corpMappingVo.getEi(), orderModel);
        Boolean allow = Boolean.valueOf(refuseMap.get("allow").toString());
        List<OrderModel> modelList = Lists.newArrayList();
        if (allow) {
            //允许同步订单
            if (!orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE) && !"TIANYUAN".equals(orderModel.getOrderCreatSource())) {
                if (ObjectUtils.isEmpty(refuseMap.get("current_version"))) {
                    //如果空，说明企业过期，可以重新同步
                    modelList = splitAppOrder(orderModel, null, null);
                    List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get(modelList.get(0).getItemCode());
                    String crmProductId = neighborProductQuantities.get(0).getCrmProduct();
                    log.info("renew order crmproductId:{}.corpId:{},orderModel:{}", crmProductId, orderModel.getCorpId(), modelList);
                    createCrmOrder(modelList.get(0), corpMappingVo.getEa(), crmProductId);
                } else {
                    modelList = splitAppOrder(orderModel, corpMappingVo.getEi(), refuseMap.get("current_version").toString());
                    CRMDingProductMap current_version = ConfigCenter.CRM_DING_PRODUCT_MAP.get(refuseMap.get("current_version"));
                    createCrmOrder(modelList.get(0), corpMappingVo.getEa(), current_version.getCrmProductId());
                }

            } else {
                //不需要拆单。正常同步
                if (orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {
                    //如果云动的同事在天元拆单，对应XX通的虚拟账号或者其他数量应该需要我们自己补充账号数
                    List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get(orderModel.getItemCode());
                    Integer quantity = neighborProductQuantities.get(0).getHasRealAccountSync() ? orderModel.getSubQuantity() : neighborProductQuantities.get(0).getCrmAccount();
                    orderModel.setSubQuantity(quantity);
                    createCrmOrder(orderModel, corpMappingVo.getEa(), neighborProductQuantities.get(0).getCrmProduct());
                }
            }
            syncAttachProduct(orderModel, corpMappingVo.getEa());
            //订单问题解除封禁
            dingRefuseDataService.releaseForbid(corpMappingVo.getDingCorpId());
        }

        orderModel.setEa(enterPriseAccount);
    }

    private void syncAttachProduct(OrderModel orderModel, String ea) {
        log.info("syncAttachProduct,orderModel={},ea={}",orderModel,ea);
        //同步附属产品
        List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get(orderModel.getItemCode());
        OrderModel saveOrder = new OrderModel();
        BeanUtils.copyProperties(orderModel, saveOrder);
        Integer realQuantity = Optional.ofNullable(orderModel.getSubQuantity()).orElseGet(() -> 10);
        neighborProductQuantities.forEach(item -> {
            if (!item.getHasCrmProduct()) {//非crm才同步
                Integer quantity = item.getHasRealAccountSync() ? realQuantity : item.getSchemaAccount();
                //计费模式
                Long costFee = calculateFee(saveOrder, item);
                orderModel.setSubQuantity(quantity);
                orderModel.setPayFee(costFee);
                log.info("syncAttachProduct,orderModel2={},ea={},crmProductId={}",orderModel,ea,item.getCrmProduct());
                createCrmOrder(orderModel, ea, item.getCrmProduct());
            }
        });
    }

    @Override
    public Result<SaveOrderResult> saveOrder(OrderModel orderModel) {
        log.info("saveOrder,orderModel={}",orderModel);

        SaveOrderResult saveOrderResult = new SaveOrderResult();
        saveOrderResult.setOrderModel(orderModel);
        saveOrderResult.setOldGoodsCode(orderModel.getGoodsCode());

        //crm的试用订单配额为全公司的配额
        //String ea="";
        if (orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {
            if (("TRYOUT".equals(orderModel.getOrderChargeType()))) {
                //试用订单提供的人数是设置成企业人数
//                Integer empCount = dingManager.getEmpCount(orderModel.getCorpId(), orderModel.getSuiteId());
//                orderModel.setSubQuantity(empCount);
                //默认为 10000
                orderModel.setSubQuantity(ConfigCenter.createCrmAccount);
            }
//            Map<String, Object> objectMap = initEnterPrise(orderModel);
//            if(!Boolean.valueOf(objectMap.get("hasContinue").toString())){
//                return false;
//            }
//            ea=objectMap.get("ea").toString();
        } else {
            List<OrderModel> modelList = splitAppOrder(orderModel, null, null);
            log.info("saveOrder,splitAppOrder,modelList={}",modelList);
            //非CRM应用的需要创建企成功后，同步附属产品
            orderModel = modelList.get(0);
//            Map<String, Object> objectMap = initEnterPrise(modelList.get(0));//先同步crm订单
//            if(!Boolean.valueOf(objectMap.get("hasContinue").toString())){
//                return false;
//            }
//            ea = objectMap.get("ea").toString();
//            //继续同步附属产品
//            syncAttachProduct(orderModel, ea);
        }
        //保存订单
//        orderModel.setEa(ea);
//        log.info("saveOrder,orderModel={}",orderModel);
//        Integer orderCount = cloudOrderService.addOrder(orderModel);
//        log.info("saveOrder,addOrder,orderCount={}",orderCount);
        log.info("saveOrder,saveOrderResult={}",saveOrderResult);
        return Result.newSuccess(saveOrderResult);
    }

    @Override
    public Result<Void> saveOrderAndAddFsOrder(OrderModel orderModel) {
        log.info("saveOrderAndAddFsOrder,orderModel={}",orderModel);
        switch (orderModel.getOrderType()) {
            case NEW_TYPE:
                newOrder(orderModel);
                break;
            case RENEW:
                renewOrder(orderModel);
                break;
            case UPGRADE:
                upgradeOrder(orderModel);
                break;
            default:
                renewOrder(orderModel);
        }
        //保存订单
        Integer orderCount = cloudOrderService.addOrder(orderModel);
        log.info("saveOrderAndAddFsOrder,addOrder={}", orderCount);
        //避免前面高级事件查询的时候，漏掉bizType=4的事件，这边添加处理，处理完订单后，主动触发bizType=4
        //个人组织的不同步组织架构，因为人员所属部门读取不到

        HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(4, orderModel.getCorpId(), orderModel.getSuiteId());
        log.info("saveOrderAndAddFsOrder,selectBizTypeByCorpId,bizDataDo={}", bizDataDo);
        if(ObjectUtils.isNotEmpty(bizDataDo)){
            serviceMap.get(bizDataDo.getBizType()).executeEvent(bizDataDo);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<CreateFsCustomerResult> createFsCustomer(OrderModel orderModel) {
        log.info("createFsCustomer,orderModel={}",orderModel);
        StopWatch stopWatch = StopWatch.create("createFsCustomer");
//        Map<String, Object> nameMap = Maps.newHashMap();
//        nameMap.put("hasContinue",true);

        CreateFsCustomerResult createFsCustomerResult = new CreateFsCustomerResult();

        String userId = dingManager.getUserIdByUnionId(orderModel.getCorpId(), orderModel.getUnionId(), orderModel.getSuiteId());
        log.info("createFsCustomer,userId={}",userId);

        // 添加个人授权应用的处理
        List<EmployeeDingVo> dingEmp = dingManager.getDingEmp(orderModel.getCorpId(), Lists.newArrayList(userId), orderModel.getSuiteId());
        log.info("createFsCustomer,dingEmp={}",dingEmp);

        //获取授权企业信息
        AuthEnterPriseModel.BizData enterPriseData = dingManager.getEnterPriseData(orderModel.getCorpId(), orderModel.getSuiteId());
        log.info("createFsCustomer,enterPriseData={}",enterPriseData);
        stopWatch.lap("getEnterPriseData");
        //去掉空格
        String enterPriseName = PinyinUtils.saveSpecialSymbol(enterPriseData.getAuthCorpInfo().getCorpName()).replace(" ", "");
        if (isNumeric(enterPriseName)) {
            log.info("createFsCustomer,enterprise name not support fullNumber:{},enterpriseName:{}", orderModel.getOrderId(), enterPriseName);
            return Result.newError(ResultCode.ENTERPRISE_NAME_NOT_SUPPORT_ALL_NUMBER);
        }
        //创建客户
        String enterPriseAccount = createCustomEnterpriseAccount(enterPriseName);
        log.info("createFsCustomer,enterPriseAccount={}", enterPriseAccount);
        CreateCustomerArg createCustomerArg = CreateCustomerArg.builder().enterpriseAccount(enterPriseAccount).
                enterpriseName(enterPriseName).managerName(ValidNameUtils.employeeValidName(dingEmp.get(0).getName())).source(1).outEid(orderModel.getCorpId())
                .build();
        Result<String> customer = crmManager.createCustomer(createCustomerArg);
        log.info("createFsCustomer,createCustomer,customer={}", customer);
        if (!customer.isSuccess()) {
            log.warn("createFsCustomer,create customer error:{},corpId:{}", customer, orderModel.getCorpId());
            //一秒扫描，现在绑定开通，一个企业可能同时开通两个应用。会出现创建客户失败的问题
            openSyncBizDataDao.updateModifyTimeByBizId(getAfterTime(),String.valueOf(orderModel.getOrderId()),orderModel.getCorpId());
            //nameMap.put("hasContinue",false);
            //return nameMap;
            return Result.newError(customer.getErrorCode(),customer.getErrorMessage());
        }
        stopWatch.lap("createCustomer");
//        nameMap.put("ea", enterPriseAccount);
//        nameMap.put("en", enterPriseName);
        createFsCustomerResult.setEa(enterPriseAccount);
        createFsCustomerResult.setEn(enterPriseName);
        return Result.newSuccess(createFsCustomerResult);
    }

    @Override
    public Result<String> createFsOrder(SaveOrderResult saveOrderResult, String enterPriseAccount, String enterPriseName) {
        OrderModel orderModel = saveOrderResult.getOrderModel();
        log.info("createFsOrder,saveOrderResult={},enterPriseAccount={},enterPriseName={}",saveOrderResult,enterPriseAccount,enterPriseName);
        StopWatch stopWatch = StopWatch.create("createFsOrder");

        String userId = dingManager.getUserIdByUnionId(orderModel.getCorpId(), orderModel.getUnionId(), orderModel.getSuiteId());
        log.info("createFsOrder,userId={}",userId);
        // 添加个人授权应用的处理
        List<EmployeeDingVo> dingEmp = dingManager.getDingEmp(orderModel.getCorpId(), Lists.newArrayList(userId), orderModel.getSuiteId());
        log.info("createFsOrder,dingEmp={}",dingEmp);

        //创建订单
        //先创建crm 应用
        String productId = ConfigCenter.GOODS_CODE.get(orderModel.getItemCode());
        Result<String> createOrder = createCrmOrder(orderModel, enterPriseAccount, productId);
        log.info("createFsOrder,createCrmOrder,result={}", createOrder);

        final List<OuterOaEnterpriseBindEntity> entitiesByOutEa = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.dingding, orderModel.getCorpId(), null);
        if (CollectionUtils.isNotEmpty(entitiesByOutEa)) {
//        todo 950 如果已经有企业绑定,初始化数据
//            initOrganization(authModel, crmResult,suiteId);
        } else {
            //创建一个线程，一分钟后检查企业是否创建成功
            new Thread(() -> enterpriseOpenMonitor(orderModel.getCorpId(), orderModel.getSuiteId(), enterPriseAccount)).start();
        }

        String cacheKey=String.format(WAITING_TENANT_ACCOMPLISH_EVENT,enterPriseAccount);
        WaitingTenantCreateArg waitingTenantCreateArg= WaitingTenantCreateArg.builder().dingCorpId(orderModel.getCorpId())
                .dingCreateEmpVo(dingEmp.get(0)).enterpriseName(enterPriseName).orderModel(orderModel).build();
        //订单创建成功后，缓存未开通成功的企业信息到redis上，等企业创建成功后，再取出来用。
        redisDataSource.getRedisClient().setex(cacheKey,EXPIRE_TIME_SECONDS,JSONObject.toJSONString(waitingTenantCreateArg));

        log.info("createFsOrder,redisDataSource save result:{},{}", cacheKey,waitingTenantCreateArg);

        stopWatch.lap("createFsOrder");
        stopWatch.log();

        if (!saveOrderResult.getOldGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {
            log.info("createFsOrder,not crm main product,syncAttachProduct");
            //继续同步附属产品
            syncAttachProduct(orderModel, enterPriseAccount);
        }
        orderModel.setEa(enterPriseAccount);

        //订单入库
        Integer orderCount = cloudOrderService.addOrder(orderModel);
        log.info("createFsOrder,addOrder,orderCount={}",orderCount);

        return createOrder;
    }

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
//        Result<List<DingCorpMappingVo>> result = dingCorpMappingService.queryByEa(ea);
//        return Result.newSuccess(CollectionUtils.isNotEmpty(result.getData()));
        String cacheKey=String.format(WAITING_TENANT_ACCOMPLISH_EVENT,ea);
        String tenantValue = redisDataSource.getRedisClient().get(cacheKey);
        log.info("isEnterpriseBind,cacheKey={},tenantValue={}",cacheKey,tenantValue);
        return Result.newSuccess(StringUtils.isNotEmpty(tenantValue));
    }

    public Map<String, Object> initEnterPrise(OrderModel orderModel) {
        log.info("initEnterPrise,orderModel={}",orderModel);
        StopWatch stopWatch = StopWatch.create("initEnterPrise");
        Map<String, Object> nameMap = Maps.newHashMap();
        nameMap.put("hasContinue",true);
        log.info("init enterPrise model:{}", orderModel);
        String userId = dingManager.getUserIdByUnionId(orderModel.getCorpId(), orderModel.getUnionId(), orderModel.getSuiteId());
        log.info("initEnterPrise,userId={}",userId);
        List<EmployeeDingVo> dingEmp = Lists.newArrayList();

        // 添加个人授权应用的处理
        dingEmp = dingManager.getDingEmp(orderModel.getCorpId(), Lists.newArrayList(userId), orderModel.getSuiteId());
        log.info("initEnterPrise,dingEmp={}",dingEmp);

        //获取授权企业信息
        AuthEnterPriseModel.BizData enterPriseData = dingManager.getEnterPriseData(orderModel.getCorpId(), orderModel.getSuiteId());
        log.info("initEnterPrise,enterPriseData={}",enterPriseData);
        stopWatch.lap("getEnterPriseData");
        //去掉空格
        String enterPriseName = PinyinUtils.saveSpecialSymbol(enterPriseData.getAuthCorpInfo().getCorpName()).replace(" ", "");
        if (isNumeric(enterPriseName)) {
            log.info("enterprise name not support fullNumber:{},enterpriseName:{}", orderModel.getOrderId(), enterPriseName);
            return null;
        }
        //创建客户
        String enterPriseAccount = createCustomEnterpriseAccount(enterPriseName);
        log.info("init enterPrise account:{}", enterPriseAccount);
        CreateCustomerArg createCustomerArg = CreateCustomerArg.builder().enterpriseAccount(enterPriseAccount).
                enterpriseName(enterPriseName).managerName(ValidNameUtils.employeeValidName(dingEmp.get(0).getName())).source(1).outEid(orderModel.getCorpId())
                .build();
        Result<String> customer = crmManager.createCustomer(createCustomerArg);
        if (!customer.isSuccess()) {
            log.warn("create customer error:{},corpId:{}", customer, orderModel.getCorpId());
            //一秒扫描，现在绑定开通，一个企业可能同时开通两个应用。会出现创建客户失败的问题
            openSyncBizDataDao.updateModifyTimeByBizId(getAfterTime(),String.valueOf(orderModel.getOrderId()),orderModel.getCorpId());
            nameMap.put("hasContinue",false);
            return nameMap;
        }
        //TODO 如果客户重名 加后缀
        stopWatch.lap("customer");
        log.info("enterprise auth create customer result:{}", customer);
        //创建订单
        //先创建crm 应用
        String productId = ConfigCenter.GOODS_CODE.get(orderModel.getItemCode());
        Result<String> createOrder = createCrmOrder(orderModel, enterPriseAccount, productId);
        log.info("order event result:{},{},{}", createOrder,enterPriseAccount,orderModel.getCorpId());
        //创建一个线程，一分钟后检查企业是否创建成功
        new Thread(() -> enterpriseOpenMonitor(orderModel.getCorpId(), orderModel.getSuiteId(), enterPriseAccount)).start();
        String cacheKey=String.format(WAITING_TENANT_ACCOMPLISH_EVENT,enterPriseAccount);
        WaitingTenantCreateArg waitingTenantCreateArg= WaitingTenantCreateArg.builder().dingCorpId(orderModel.getCorpId())
                .dingCreateEmpVo(dingEmp.get(0)).enterpriseName(enterPriseName).orderModel(orderModel).build();
        redisDataSource.getRedisClient().setex(cacheKey,EXPIRE_TIME_SECONDS,JSONObject.toJSONString(waitingTenantCreateArg));
        log.info("redisDataSource save result:{},{}", cacheKey,waitingTenantCreateArg);
        stopWatch.lap("createOrder");
        nameMap.put("ea", enterPriseAccount);
        stopWatch.log();
        return nameMap;
    }

    private Date getAfterTime() {
        Date date = new Date();
        Date afterDate = new Date(date.getTime() + 5000);
        return afterDate;
    }

    /**
     * 输入字符串是否全由数字组成
     *
     * @param str
     * @return
     */
    public boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    //续费
    private void renewOrder(OrderModel orderModel) {
        log.info("renewOrder,orderModel={}",orderModel);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId());
        final String appId = appParams.getAppId();
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(orderModel.getCorpId(), appId);
        String ea = corpResult.getData().get(0).getEa();
        existsEACreateOrder(orderModel, corpResult.getData().get(0));
        orderModel.setEa(ea);
        log.info("renewOrder,orderModel2={}",orderModel);
    }

    //升级
    private void upgradeOrder(OrderModel orderModel) {
        log.info("upgradeOrder,orderModel={}",orderModel);
//        //查询上一次订单信息
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder(orderModel.getCorpId(), orderModel.getSuiteId());
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId());
        final String appId = appParams.getAppId();
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(orderModel.getCorpId(), appId);
        String ea = corpResult.getData().get(0).getEa();
        if (orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {

            String upgradeItemCode = "";
            if (!lastOrder.getSubQuantity().equals(orderModel.getSubQuantity())) {
                //升级人数
                Integer addQuantity = orderModel.getSubQuantity() - lastOrder.getSubQuantity();
                orderModel.setSubQuantity(addQuantity);
            } else {
                //升版本
                if (orderModel.getItemCode().equals(ConfigCenter.DING_STRENGTHEN_CODE)) {
                    //升级旗舰版
                    String upgradeCode = String.format(upgrade_strengthen, lastOrder.getItemCode());
                    upgradeItemCode = StringUtils.isNotEmpty(ConfigCenter.GOODS_CODE.get(upgradeCode)) ? upgradeCode : lastOrder.getItemCode();

                } else {
                    //升级专业版
                    String upgradeCode = String.format(upgrade_standPro, lastOrder.getItemCode());
                    upgradeItemCode = StringUtils.isNotEmpty(ConfigCenter.GOODS_CODE.get(upgradeCode)) ? upgradeCode : lastOrder.getItemCode();
                }
                orderModel.setItemCode(upgradeItemCode);
            }

            orderModel.setEa(ea);
            List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get(orderModel.getItemCode());
            log.info("upgradeOrder,neighborProductQuantities={}",neighborProductQuantities);
            String crmProductId = neighborProductQuantities.get(0).getCrmProduct();
            log.info("upgradeOrder,createCrmOrder,orderModel={},ea={},crmProductId={}",orderModel,ea,crmProductId);
            Result<String> upgradeResult = createCrmOrder(orderModel, ea, crmProductId);
            log.info("upgradeOrder,createCrmOrder,upgradeResult={}",upgradeResult);
        } else {
            //对于应用升级
            syncAttachProduct(orderModel, ea);
        }

    }


    private Result<Integer> insertCorpMapping(String ea, Integer ei, OrderModel orderModel, String enterPriseName) {
        String dingCorpId = orderModel.getCorpId();
        log.info("insert mapping orderModel:{},marketing:{}", orderModel, ConfigCenter.MARKETING_APP_MAPPING);
        AppParams appParams = Optional.ofNullable(ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId())).orElse(ConfigCenter.APP_PARAMS_MAP.get(ConfigCenter.CRM_SUITE_ID));

        DingCorpMappingVo corpMappingVo = DingCorpMappingVo.builder().ea(ea).ei(ei).dingCorpId(dingCorpId).enterpriseName(enterPriseName)
                .appCode(Long.parseLong(appParams.getAppId())).bindType(BindType.ORDER_BIND.getType()).isInit(0).dingMainCorpId(orderModel.getMainCorpId()).extend(GlobalValue.enterprise_extend).build();
        log.info("insertCorpMapping,corpMappingVo={}",corpMappingVo);
        Result<Integer> corpCount = dingCorpMappingService.insertCorpMapping(corpMappingVo);
        log.info("insertCorpMapping,corpCount={}",corpCount);
        return corpCount;
    }


    //企业创建，会默认创建客户的负责人，1000
    private Integer createManager(EmployeeDingVo employeeDingVo, Integer ei, String appId) {
        DingMappingEmployeeResult employeeResult = new DingMappingEmployeeResult();
        employeeResult.setBindStatus(2);
        employeeResult.setEi(ei);
        employeeResult.setEmployeeStatus(1);
        employeeResult.setDingEmployeeStatus(1);
        employeeResult.setEmployeeName(employeeDingVo.getName());
        employeeResult.setDingDeptId(employeeDingVo.getMainDept());
        employeeResult.setDingUnionId(employeeDingVo.getUnionid());
        employeeResult.setDingEmployeeId(employeeDingVo.getUserid());
        employeeResult.setDingEmployeeName(employeeDingVo.getName());
        employeeResult.setEmployeeId(1000);
        Result<Integer> createCount = cloudEmpService.insertEmpData(Lists.newArrayList(employeeResult), appId);
        return createCount.getData();
    }

    /**
     * 创建纷享的订单
     */
    public Result<String> createCrmOrder(OrderModel orderModel, String enterpriseAccount, String crmProductId) {
        StopWatch stopWatch = StopWatch.create("createCrmOrder");
        CreateCrmOrderArg createCrmOrderArg = new CreateCrmOrderArg();
        CreateCrmOrderArg.CrmOrderDetailInfo crmOrderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        crmOrderDetailInfo.setOrderId(orderModel.getOrderId().toString());
        crmOrderDetailInfo.setEnterpriseAccount(enterpriseAccount);
        Integer orderType = orderModel.getItemCode().equals(ConfigCenter.TRY_GOOD) ? ConfigCenter.ORDER_TRY_TYPE : ConfigCenter.ORDER_BUY_TYPE;
        crmOrderDetailInfo.setOrderTpye(orderType);
        crmOrderDetailInfo.setOrderTime(orderModel.getPaidTime());
        createCrmOrderArg.setCrmOrderDetailInfo(crmOrderDetailInfo);
        CreateCrmOrderArg.CrmOrderProductInfo crmOrderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        crmOrderProductInfo.setProductId(crmProductId);
        crmOrderProductInfo.setQuantity(orderModel.getSubQuantity());
        crmOrderProductInfo.setAllResourceCount(orderModel.getSubQuantity());
        //钉钉推送的金额是分
        Double payCount = Double.parseDouble(orderModel.getPayFee().toString()) / 100;
        crmOrderProductInfo.setOrderAmount(payCount.toString());
        Date fromDate = new Date(orderModel.getServiceStartTime());
        Date endDate = new Date(orderModel.getServiceStopTime());
        crmOrderProductInfo.setBeginTime(fromDate.getTime());
        crmOrderProductInfo.setEndTime(endDate.getTime());
        createCrmOrderArg.setCrmOrderProductInfo(crmOrderProductInfo);
        log.info("createCrmOrder,createCrmOrderArg={}",createCrmOrderArg);
        Result<String> orderResult = crmManager.createCrmOrder(createCrmOrderArg);
        log.info("createCrmOrder,orderResult={}",orderResult);
        stopWatch.lap("createOrder");
        stopWatch.log();
        log.info("create crm order arg :{} result:{},orderModel:{}", createCrmOrderArg, orderResult, orderModel);
        return orderResult;
    }


//    //下单人、企业管理人员设置系统管理人员
//    private void createSystemRole(EmployeeDingVo employeeDingVo, Integer ei, Long appId, String dingCorpId) {
//        log.info("batch createSystemRole :{}", employeeDingVo);
//        cloudEmpService.cloudCreateEmp(employeeDingVo, ei, appId, dingCorpId);
//        Result<DingMappingEmployeeResult> managerResult = objectMappingService.queryEmpByDingUserId(ei, employeeDingVo.getUserid());
//        Integer empId = managerResult.getData().getEmployeeId();
//        //设置系统管理员
//        Result<String> roleResult = crmManager.addManagerRole(ei, empId);
//        log.info("create managerResult:{}", roleResult);
//    }

    private void updateUser(EmployeeDingVo employeeDingVo, Integer ei, Long appId, String dingCorpId) {
        Result<Integer> result = cloudEmpService.cloudUpdateEmp(employeeDingVo, ei, appId, dingCorpId);
        log.info("updateUser managerResult:{}", result);
    }


    /**
     * 订单切割 客户自主下单XX通。需要后台分单为 crm订单与XX通订单。天元系统的云动同事会手动分单。推送两条订单数据
     *
     * @param ei         当前crm版本。专业版的。XX通订单拆分为 专业版CRM+5账号 +XX通订单
     *                   旗舰版的 XX通订单拆分为 旗舰版CRM+3账号  +xx通订单
     * @param crmVersion 当前crm的版本
     */
    public List<OrderModel> splitAppOrder(OrderModel orderModel, Integer ei, String crmVersion) {
        List<OrderModel> modelList = Lists.newArrayList();
        if (ObjectUtils.isEmpty(ei)) {
            //需要拆分CRM订单+XX通订单
            OrderModel crmOrder = new OrderModel();
            BeanUtils.copyProperties(orderModel, crmOrder);
            //TODO 支持下获取crm版本对应的productId
            List<NeighborProductQuantity> neighborProductQuantities = ConfigCenter.MORE_APP_NEIGHBOR.get(orderModel.getItemCode());
            //如果为空，默认取第一个crm版本，第一个版本不能错乱
            Integer proCrmAccount = neighborProductQuantities.get(0).getCrmAccount();//赠送的crm账号数
            crmOrder.setPayFee(0L);
            crmOrder.setSubQuantity(proCrmAccount);
            crmOrder.setItemCode(neighborProductQuantities.get(0).getCRMDingItemCode());
            crmOrder.setGoodsCode(ConfigCenter.CRM_GOODS_CODE);
            //支持永久免费版本的crm（服务通，营销通，从钉钉客户管理开通的，默认永久开通3个账号的标准版crm)
//            if(ConfigCenter.FOREVER_FREE_ITEM_CODE.contains(orderModel.getItemCode())){
//                crmOrder.setSubQuantity(3);
//            }
            modelList.add(crmOrder);
            modelList.add(orderModel);
            return modelList;
        } else {
            //查询当前crm版本。决定赠送的crm数量
            //添加crm的订单
            OrderModel crmOrder = new OrderModel();
            BeanUtils.copyProperties(orderModel, crmOrder);
            //TODO 根据itemcode需要处理对应的crmversion
            if (ConfigCenter.FOREVER_FREE_ITEM_CODE.contains(orderModel.getItemCode())) {
//                crmOrder.setServiceStopTime(System.currentTimeMillis()+100*365*24*60*60);//添加100年免费使用的订单
//                crmOrder.setSubQuantity(3);
                crmVersion = "dingtalk_standard_edition";//默认是标准版
            }
            CRMDingProductMap crmDingProductMap = ConfigCenter.CRM_DING_PRODUCT_MAP.get(crmVersion);//根据crm版本定位crm产品ID
            NeighborProductQuantity neighborProductQuantity = crmAccount(orderModel.getItemCode(), crmDingProductMap.getCrmProductId());
            crmOrder.setPayFee(0L);
            crmOrder.setGoodsCode(ConfigCenter.CRM_GOODS_CODE);
            crmOrder.setItemCode(crmDingProductMap.getDingItemCode());
            crmOrder.setSubQuantity(neighborProductQuantity.getCrmAccount());
            modelList.add(crmOrder);
            modelList.add(orderModel);
            return modelList;
        }
    }

    //判断订单能否顺利同步
    public Map<String, Object> refuseOrder(Integer ei, OrderModel orderModel) {
        QueryProductArg queryProductArg = new QueryProductArg();
        Map<String, Object> refuseMap = Maps.newHashMap();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setTenantId(ei.toString());
        licenseContext.setAppId("CRM");
        licenseContext.setUserId("-10000");
        queryProductArg.setLicenseContext(licenseContext);
        LicenseVersionResult licenseVersionResult = crmManager.queryCrmLicense(queryProductArg);
        List<ProductVersionPojo> crmVersion = licenseVersionResult.getResult().stream().filter(item -> item.getProductType().equals("0")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmVersion) || ConfigCenter.FOREVER_FREE_ITEM_CODE.contains(orderModel.getItemCode())) {
            //过期后查询不到，直接允许同步
            log.info("query crm version fail:{}", crmVersion);
//            saveRefuseData(ei, orderModel);
            refuseMap.put("allow", true);
            refuseMap.put("init", true);
            return refuseMap;
        }
        String currentVersion = crmVersion.get(0).getCurrentVersion();
        if (orderModel.getGoodsCode().equals(ConfigCenter.CRM_GOODS_CODE)) {
            //购买纷享销客的订单,如果订单的crm版本小于现在的版本。则拒绝订单
            Integer currentCrmWeight = ConfigCenter.CRM_VERSION_WEIGHT.get(currentVersion);
            Integer orderCrmWeight = ConfigCenter.DING_VERSION_WEIGHT.get(orderModel.getItemCode());
            if (orderCrmWeight < currentCrmWeight) {
                log.info("order not accept,crm version conflict:{},crm Verison:{}", orderModel, currentVersion);
                // 保存失败的订单信息
                saveRefuseData(ei, orderModel);
                refuseMap.put("allow", false);
                return refuseMap;
            }
        }
//        else {
//            //应用的订单，如果是订单的crm版本大于现在的crm版本，则拒绝订单
//            Integer currentCrmWeight = ConfigCenter.CRM_VERSION_WEIGHT.get(currentVersion);
//            if (currentCrmWeight == 1) {
//                //标准版
//                log.info("order not accept,crm version conflict:{},crm Verison:{}", orderModel, currentVersion);
//                saveRefuseData(ei, orderModel);
//                refuseMap.put("allow", false);
//                return refuseMap;
//            }
//        }
        refuseMap.put("allow", true);
        refuseMap.put("current_version", currentVersion);
        return refuseMap;
    }

    private Integer saveRefuseData(Integer ei, OrderModel orderModel) {
        DingRefuseDataModel dingRefuseDataModel = new DingRefuseDataModel();
        dingRefuseDataModel.setBizData(JSONObject.toJSONString(orderModel));
        dingRefuseDataModel.setOrderId(orderModel.getOrderId());
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId());
        dingRefuseDataModel.setRefuseSuiteId(orderModel.getSuiteId());
        dingRefuseDataModel.setRefuseAppId(Integer.valueOf(appParams.getAppId()));
        dingRefuseDataModel.setCorpId(orderModel.getCorpId());
        dingRefuseDataModel.setActiveStatus(1);
        dingRefuseDataModel.setEi(ei);
        Result<Integer> result = dingRefuseDataService.insertOrderData(dingRefuseDataModel);
        return result.getData();
    }


    /**
     * 生成自定义ea
     */
    public String createCustomEnterpriseAccount(String enterpriseName) {
        StringBuilder enterPriseName = new StringBuilder();
        Long incrId = RedisLockUtils.getIncrId(redisDataSource.getRedisClient());
        if (incrId < 1000) {
            //避免redis内存不足，淘汰一些value
            incrId = Math.round((Math.random() + 1) * 1000);
        }
        String suffix = String.valueOf(incrId);
        String ea = ENTERPRISE_PREFIX + PinyinUtils.converterToFirstSpell(enterpriseName);
        enterPriseName.append(StringUtils.substring(ea, 0, 6)).append(suffix);
        log.info("createCustomEnterpriseAccount result :{}", enterPriseName.toString());
        return enterPriseName.toString();
    }

    public void receiveTenantCreateEvent(String ea, Integer ei, String appId){
        String cacheKey=String.format(WAITING_TENANT_ACCOMPLISH_EVENT,ea);
        String tenantValue = redisDataSource.getRedisClient().get(cacheKey);
        log.info("receiveTenantCreateEvent,cacheKey={},tenantValue={}",cacheKey,tenantValue);
        if(StringUtils.isEmpty(tenantValue)){
            return;
        }
        WaitingTenantCreateArg waitingTenantCreateArg = JSONObject.parseObject(tenantValue, WaitingTenantCreateArg.class);
        log.info("receiveTenantCreateEvent,waitingTenantCreateArg={}",waitingTenantCreateArg);

        //插入管理员映射数据到员工映射表
        createManager(waitingTenantCreateArg.getDingCreateEmpVo(), ei, appId);
        Result<Integer> countMapping = insertCorpMapping(ea, ei, waitingTenantCreateArg.getOrderModel(), waitingTenantCreateArg.getEnterpriseName());
        log.info("init corp mapping count:{},corpId:{},ei:{}", countMapping, waitingTenantCreateArg.getDingCorpId(), ei);
        //保存订单
        //避免前面高级事件查询的时候，漏掉bizType=4的事件，这边添加处理，处理完订单后，主动触发bizType=4
        //个人组织的不同步组织架构，因为人员所属部门读取不到

        HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(4, waitingTenantCreateArg.getDingCorpId(), waitingTenantCreateArg.getOrderModel().getSuiteId());
        if(ObjectUtils.isNotEmpty(bizDataDo)){
            Thread thread = new Thread() {
                public void run() {
                    serviceMap.get(bizDataDo.getBizType()).executeEvent(bizDataDo);
                }
            };
            thread.start();
        }
        redisDataSource.getRedisClient().del(cacheKey);
        log.info("receiveTenantCreateEvent fix bizType4 data:{}", bizDataDo);
    }

    @Override
    public Result<WaitingTenantCreateArg> updateEnterpriseAndAdminMapping(String ea, Integer ei) {
        log.info("updateEnterpriseAndAdminMapping,ea={},ei={}",ea,ei);
        String cacheKey=String.format(WAITING_TENANT_ACCOMPLISH_EVENT,ea);
        String tenantValue = redisDataSource.getRedisClient().get(cacheKey);
        log.info("updateEnterpriseAndAdminMapping,cacheKey={},tenantValue={}",cacheKey,tenantValue);
        if(StringUtils.isEmpty(tenantValue)){
            log.info("updateEnterpriseAndAdminMapping,tenantValue is empty");
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        WaitingTenantCreateArg waitingTenantCreateArg = JSONObject.parseObject(tenantValue, WaitingTenantCreateArg.class);
        log.info("updateEnterpriseAndAdminMapping,waitingTenantCreateArg={}",waitingTenantCreateArg);

        Result<Integer> corpMapping = insertCorpMapping(ea, ei, waitingTenantCreateArg.getOrderModel(), waitingTenantCreateArg.getEnterpriseName());
        log.info("updateEnterpriseAndAdminMapping,insertCorpMapping,corpMapping={}", corpMapping);

        // 根据创建企业时，最多只会有一个钉钉订单,数据库里只会有一个绑定关系,所以取第一个就好
        final List<OuterOaEnterpriseBindEntity> entitiesByFsEa = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.dingding, ea);
        final OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = entitiesByFsEa.get(0);
        final String appId = outerOaEnterpriseBindEntity.getAppId();

        // 初始化配置
        initSettingsResult(outerOaEnterpriseBindEntity);

        //插入管理员映射数据到员工映射表
        Integer count = createManager(waitingTenantCreateArg.getDingCreateEmpVo(), ei, appId);
        log.info("updateEnterpriseAndAdminMapping,insertCorpMapping,count={}", count);

        redisDataSource.getRedisClient().del(cacheKey);

        return Result.newSuccess(waitingTenantCreateArg);
    }

    public Integer initSettingsResult(OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        final String dcId = enterpriseBindEntity.getId();
        final SettingsResult settingsResult = convert2SettingsResult(dcId);

        final OuterOaConfigInfoEntity entity = new OuterOaConfigInfoEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setDcId(dcId);
        entity.setFsEa(enterpriseBindEntity.getFsEa());
        entity.setOutEa(enterpriseBindEntity.getOutEa());
        entity.setAppId(enterpriseBindEntity.getAppId());
        entity.setType(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES);
        entity.setConfigInfo(com.alibaba.fastjson2.JSON.toJSONString(settingsResult));
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return outerOaConfigInfoManager.upsertByDcIdAndType(entity);
    }

    private static SettingsResult convert2SettingsResult(String dcId) {
        final SettingsResult settingsResult = new SettingsResult();
        settingsResult.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountSync);
        settingsResult.setBindTypeEnum(BindTypeEnum.auto);

        final SettingAccountRulesModel.EmployeeRangeRemoveRule employeeRangeRemoveRule = new SettingAccountRulesModel.EmployeeRangeRemoveRule();
        employeeRangeRemoveRule.setUnbind(true);
        employeeRangeRemoveRule.setStopEmp(true);
        settingsResult.setEmployeeRangeRemoveRule(employeeRangeRemoveRule);
        final SettingAccountRulesModel.EmployeeLeaveRule employeeLeaveRule = new SettingAccountRulesModel.EmployeeLeaveRule();
        employeeLeaveRule.setUnbind(true);
        employeeLeaveRule.setStopEmp(true);
        settingsResult.setEmployeeLeaveRule(employeeLeaveRule);

        settingsResult.setEmployeeFieldMapping(Lists.newArrayList());
        settingsResult.setDeptFieldMappings(Lists.newArrayList());
        settingsResult.setCurrentDcId(dcId);

        return settingsResult;
    }

    @Override
    public Result<Void> sendWelcomeMsg(String ea) {
        log.info("sendWelcomeMsg,ea={}",ea);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initEnterpriseContacts(String ea, String outEa, String suiteId) {
        log.info("initEnterpriseContacts,ea={},outEa={},suiteId={}",ea,outEa,suiteId);
        //避免前面高级事件查询的时候，漏掉bizType=4的事件，这边添加处理，处理完订单后，主动触发bizType=4
        //个人组织的不同步组织架构，因为人员所属部门读取不到

        HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(4, outEa, suiteId);
        if(ObjectUtils.isNotEmpty(bizDataDo)){
            Thread thread = new Thread() {
                public void run() {
                    serviceMap.get(bizDataDo.getBizType()).executeEvent(bizDataDo);
                }
            };
            thread.start();
        }

        return Result.newSuccess();
    }

    private void enterpriseOpenMonitor(String outEa, String appId, String fsEa) {
        log.info("OrderServiceImpl.enterpriseOpenMonitor,outEa={},appId={}", outEa, appId);
        //睡眠一分钟 呼呼大睡
        try {
            Thread.sleep(60 * 1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //查库
        List<OuterOaEnterpriseBindEntity> mappingResult = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.dingding, outEa, null);
        if(CollectionUtils.isEmpty(mappingResult)) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .ea(fsEa)
                    .appId(appId)
                    .channelId(SourceTypeEnum.DING_CLOUD.name())
                    .dataTypeId(CloudDataTypeEnum.ENTERPRISE_CREATE.getDataType())
                    .corpId(outEa)
                    .errorCode("100")
                    .errorMsg("超过一分钟，该企业还未创建成功，请及时关注！")
                    .build();
            oaConnectorOpenDataManager.send(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("钉钉云钉钉企业开通失败告警");
            String msg = String.format("超过一分钟，该企业还未创建成功\n纷享企业ea=%s\n请及时关注！", fsEa);
            arg.setMsg(msg);
            cloudNotificationService.sendDingCloudNotice(arg);
        }
    }


    public static void main(String[] args) {
        OrderEvent17ServiceImpl orderEvent17Service = new OrderEvent17ServiceImpl();
        String customEnterpriseAccount = orderEvent17Service.createCustomEnterpriseAccount("纷享销客CRM钉钉版–样板间");
        log.info(customEnterpriseAccount);
    }
}
