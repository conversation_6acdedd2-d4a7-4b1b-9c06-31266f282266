package com.facishare.open.ding.api.model.connector;

import lombok.Data;

import java.io.Serializable;

@Data
public class ConnectorPushDataModel implements Serializable {
    private SubAuth SubAuth;
    private String MainEventModelId; //模型id
    private String AuthCorpId;//授权企业ID
    private Long TimeStamp;//授权时间
    private boolean IsCreate; //是否创建
    private String EventType;////事件类型，固定值=main_data_subscribe
    private TargetApp targetApp;

    @Data
    public static class SubAuth {
        private boolean create;
        private boolean read;
        private boolean update;
        private boolean delete;
    }

    @Data
    public static class TargetApp {
        private String appId;
        private String appName;
        private String connectorId;//连接器id
        private String triggerId;//触发器id
        private String triggerName;
        private String actionId;
        private String actionName;
        private String triggerType;//trigger类型，固定值=main_data
    }
}
