package com.facishare.open.ding.transfer.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.provider.dao.DingDeptDao;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.OutDeptData;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDeptDataManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDeptDataMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 钉钉部门扩展数据迁移处理器 - PostgreSQL
 * <AUTHOR>
 * @date 2024/3/22 15:46:25
 */
@Component
public class DingTalkIsvDeptInfoHandler extends DingTalkIsvHandler<DeptVo, List<OuterOaDeptDataEntity>> {

    @Autowired
    private DingDeptDao dingDeptDao;

    @Autowired
    private OuterOaDeptDataManager deptDataManager;

    @Autowired
    private OuterOaDeptDataMapper deptDataMapper;

    @Override
    protected void update(int enterpriseId, DeptVo sourceData, List<OuterOaDeptDataEntity> targetData) {
        final Map<String, OuterOaDeptDataEntity> collect = targetData.stream().collect(Collectors.toMap(OuterOaDeptDataEntity::getAppId, Function.identity()));
        final List<OuterOaEnterpriseBindEntity> list = appAuthEntityCache.get(sourceData.getEi());
        for (OuterOaEnterpriseBindEntity entity : list) {
            final OuterOaDeptDataEntity target = collect.get(entity.getAppId());
            final OuterOaDeptDataEntity entity1 = convert2PgEntity(sourceData, target, entity);
            if (Objects.nonNull(entity1)) {
                deptDataManager.upsert(entity1);
            }
        }
    }

    private OuterOaDeptDataEntity convert2PgEntity(DeptVo sourceData, OuterOaDeptDataEntity targetData, OuterOaEnterpriseBindEntity entity) {
        if (Objects.nonNull(targetData)) {
            final OutDeptData deptInfo = JSON.parseObject(targetData.getOutDeptInfo().toJSONString(), OutDeptData.class);
            if (checkEquals(sourceData, deptInfo)) {
                return null;
            }
        }

        final OutDeptData deptData = new OutDeptData();
        deptData.setDeptId(sourceData.getDingDeptId());
        deptData.setParentId(sourceData.getDingParentId());
        deptData.setName(sourceData.getName());
        deptData.setSeq(sourceData.getSeq());
        deptData.setOwner(sourceData.getDingDeptOwner());

        OuterOaDeptDataEntity deptDataEntity = new OuterOaDeptDataEntity();
        deptDataEntity.setId(Objects.isNull(targetData) ? null : targetData.getId());
        deptDataEntity.setChannel(ChannelEnum.dingding);
        deptDataEntity.setOutEa(entity.getOutEa());
        deptDataEntity.setAppId(entity.getAppId());
        deptDataEntity.setOutDeptId(String.valueOf(sourceData.getDingDeptId()));
        deptDataEntity.setDeptName(sourceData.getName());
        deptDataEntity.setParentDeptId(sourceData.getDingParentId() != null ? String.valueOf(sourceData.getDingParentId()) : null);
        deptDataEntity.setOutDeptInfo(JSON.parseObject(JSON.toJSONString(deptData)));
        deptDataEntity.setCreateTime(Objects.isNull(targetData) ? sourceData.getCreateTime().getTime() : targetData.getCreateTime());
        deptDataEntity.setUpdateTime(Objects.isNull(targetData) ? sourceData.getUpdateTime().getTime() : targetData.getUpdateTime());

        return deptDataEntity;
    }

    @Override
    protected boolean checkDataEquals(DeptVo sourceData, List<OuterOaDeptDataEntity> targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        final Set<String> outEas;
        if (CollectionUtils.isEmpty(targetData) || targetData.size() < (outEas = appAuthEntityCache.get(sourceData.getEi()).stream().map(OuterOaEnterpriseBindEntity::getOutEa).collect(Collectors.toSet())).size()) {
            return false;
        }

        final Set<String> newOutEas = targetData.stream().map(OuterOaDeptDataEntity::getOutEa).collect(Collectors.toSet());
        if (!newOutEas.containsAll(outEas)) {
            return false;
        }

        final String outEa = outEas.stream().findFirst().orElse(null);
        final OutDeptData outDeptData = targetData.stream()
                .filter(entity -> Objects.equals(entity.getOutEa(), outEa))
                .map(entity -> JSON.parseObject(entity.getOutDeptInfo().toJSONString(), OutDeptData.class))
                .findFirst().orElse(null);

        return checkEquals(sourceData, outDeptData);
    }

    private boolean checkEquals(DeptVo sourceData, OutDeptData targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        return Objects.equals(targetData.getParentId(), sourceData.getDingParentId()) &&
                Objects.equals(targetData.getName(), sourceData.getName()) &&
                Objects.equals(targetData.getSeq(), sourceData.getSeq()) &&
                Objects.equals(targetData.getOwner(), sourceData.getDingDeptOwner());
    }

    @Override
    protected List<OuterOaDeptDataEntity> getTargetData(int enterpriseId, DeptVo k) {
        final List<String> outEas = appAuthEntityCache.get(enterpriseId).stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .distinct()
                .collect(Collectors.toList());
        return deptDataMapper.selectList(new LambdaQueryWrapper<OuterOaDeptDataEntity>()
                .eq(OuterOaDeptDataEntity::getChannel, ChannelEnum.dingding)
                .in(OuterOaDeptDataEntity::getOutEa, outEas)
                .eq(OuterOaDeptDataEntity::getOutDeptId, String.valueOf(k.getDingDeptId())));
    }

    @Override
    protected List<DeptVo> getSourceDataPage(Integer enterpriseId, DeptVo maxId) {
        return dingDeptDao.pageByCrmDeptId(enterpriseId, Objects.isNull(maxId) ? null : maxId.getCrmDeptId(), 1000);
    }
}