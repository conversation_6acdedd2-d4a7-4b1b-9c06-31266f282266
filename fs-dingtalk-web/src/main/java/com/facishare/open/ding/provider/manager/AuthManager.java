package com.facishare.open.ding.provider.manager;

import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.api.service.QueryAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by system on 2018/5/14.
 */
@Service
@Slf4j
public class AuthManager {

    @Autowired
    private QueryAppAdminService appAdminService;

    @Autowired
    private OpenAppAdminService openAppAdminService;

    @ReloadableProperty("dingtalk.appId")
    private String appId;

    /**
     * 获取应用管理员列表
     * @param ea
     * @return
     */
    public List<Integer> getAppAdmins(String ea) {
        BaseResult<List<FsUserVO>> tmpFsUserList = appAdminService.findAppAdminListByAppId(ea, appId);
        if (!tmpFsUserList.isSuccess()) {
            log.warn("Invoke appAdminService:findAppAdminListByAppId failed. ea={}, result={}", ea, tmpFsUserList);
            return null;
        }

        return tmpFsUserList.getResult().stream().map(userVo -> userVo.getUserId()).collect(Collectors.toList());
    }

    /**
     * 判断是否为应用管理员
     * @param fsUserAccount (如: E.fs.1050)
     * @return
     */
    public boolean isAppAdmin(String fsUserAccount) {
        try {
            com.facishare.open.app.center.api.result.BaseResult<Boolean> result = openAppAdminService.isAppAdmin(fsUserAccount, appId);
            log.info("Invoke com.facishare.open.app.center.api.service.OpenAppAdminService#isAppAdmin,fsUserAccount:{},appId:{},reslut:{}", fsUserAccount, appId, result);
            if (result == null) {
                log.warn("openAppAdminService.isAppAdmin, result is null! fsUserAccount:{}", fsUserAccount);
                return false;
            }
            return result.getResult();
        } catch (Exception e) {
            log.error("failed to execute AuthManager.isAppAdmin! fsUserAccount:{}", fsUserAccount, e);
            return false;
        }
    }

}
