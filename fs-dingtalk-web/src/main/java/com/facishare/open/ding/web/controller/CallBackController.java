//package com.facishare.open.ding.web.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.JSONPath;
//import com.dingtalk.api.DefaultDingTalkClient;
//import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
//import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
//import com.dingtalk.api.response.OapiUserGetuserinfoResponse;
//import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
//import com.dingtalk.oapi.lib.aes.DingTalkJsApiSingnature;
//import com.dingtalk.oapi.lib.aes.Utils;
//
//import com.facishare.open.ding.api.result.DingEnterpriseResult;
//import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
//import com.facishare.open.ding.api.service.DingtalkUserService;
//import com.facishare.open.ding.api.service.EnterpriseService;
//import com.facishare.open.ding.api.service.ObjectMappingService;
//import com.facishare.open.ding.api.service.RedisDingService;
//import com.facishare.open.ding.api.vo.DingUserVo;
//import com.facishare.open.ding.common.result.Result;
//import com.facishare.open.ding.common.result.ResultCode;
//import com.facishare.open.ding.common.utils.BeanUtil;
//import com.facishare.open.ding.common.utils.HttpUtils;
//import com.facishare.open.ding.web.base.BaseController;
//import com.facishare.open.ding.web.base.UserVo;
//import com.facishare.open.ding.web.constants.ConfigCenter;
//import com.facishare.open.ding.web.constants.Constant;
//import com.facishare.open.ding.web.dingding.DingRequestUtil;
//import com.facishare.open.ding.web.dingding.DingUrl;
//import com.facishare.open.ding.web.dingding.DingUserInfo;
//import com.facishare.open.ding.web.utils.XorUtils;
//import com.facishare.restful.common.StopWatch;
//import com.facishare.userlogin.api.model.CreateUserTokenDto;
//import com.facishare.userlogin.api.model.UserTokenDto;
//import com.facishare.userlogin.api.service.SSOLoginService;
//import com.github.autoconf.spring.reloadable.ReloadableProperty;
//import com.github.benmanes.caffeine.cache.CacheLoader;
//import com.github.benmanes.caffeine.cache.Caffeine;
//import com.github.benmanes.caffeine.cache.LoadingCache;
//import com.google.common.base.Splitter;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import com.taobao.api.ApiException;
//import jdk.nashorn.internal.ir.RuntimeNode;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.http.client.methods.CloseableHttpResponse;
//import org.apache.http.util.EntityUtils;
//import org.aspectj.lang.annotation.Before;
//import org.checkerframework.checker.nullness.qual.NonNull;
//import org.checkerframework.checker.nullness.qual.Nullable;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.ObjectUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.CrossOrigin;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestHeader;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.RestController;
//import org.springframework.web.servlet.ModelAndView;
//
//import javax.annotation.PostConstruct;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import javax.servlet.http.HttpSession;
//import java.io.IOException;
//import java.net.URLDecoder;
//import java.net.URLEncoder;
//import java.util.*;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.TimeUnit;
//
///**
// * <p>
// * 业务回调接口
// * </p>
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2019-08-20 10:03
// */
//@CrossOrigin
//@Slf4j
//@RestController
//@RequestMapping("/business")
//// IgnoreI18nFile
//public class CallBackController extends BaseController {
//
//    @Autowired
//    private EnterpriseService enterpriseService;
//
//    @Autowired
//    private ObjectMappingService objectMappingService;
//
//    @Autowired
//    private SSOLoginService ssoLoginService;
//
//    @Autowired
//    private RedisDingService redisDingService;
//
//    private static final String ADD_USER = "user_add_org";
//
//    private static final String MODIFY_USER = "user_modify_org";
//
//    private static final String LEAVE_USER = "user_leave_org";
//
//    private static final String CREATE_DEPT = "org_dept_create";
//
//    private static final String MODIFY_DEPT = "org_dept_modify";
//
//    private static final String REMOVE_DEPT = "org_dept_remove";
//
//    @ReloadableProperty("sso.redirect.url")
//    private String ssoRedirectUrl;
//
//    @ReloadableProperty("sso.source.web.url")
//    private String ssoRedirectWebUrl;
//
//    @ReloadableProperty("sso.source.detail.h5.url")
//    private String ssoSourceDetailH5Url;
//
//    @ReloadableProperty("sso.source.bpm.h5.url")
//    private String ssoSourceBpmH5Url;
//    @ReloadableProperty("h5Url")
//    private String h5Url;
//
//    // @ReloadableProperty("proxy_tenant_ids")
//    // private String proxyTenantIds;
//
//    private List<String> whiteLists = new ArrayList<>();
//    private static final String DING_REDIRECT_URL = "dingtalk://dingtalkclient/page/link?url=";
//
//    private static final String GET_USER_URL = "https://oapi.dingtalk.com/sns/getuserinfo_bycode";
//
//    ExecutorService executorService = Executors.newFixedThreadPool(20);
//    private static Integer DING_INVALID_ACCESSTOKEN = 40014;
//
//    /**
//     * 相应钉钉回调时的值
//     */
//    private static final String CALLBACK_RESPONSE_SUCCESS = "success";
//
//    private static final String CALLBACK_RESPONSE_FAIL = "fail";
//
//    // 业务流程详情页
//    private static final String BPM_URL_TYPE = "2";
//
//    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000)
//            .expireAfterWrite(80, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES)
//            .build(new CacheLoader<String, String>() {
//                @Nullable
//                @Override
//                public String load(@NonNull String key) throws Exception {
//                    Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService
//                            .queryEnterpriseByEi(Integer.valueOf(key));
//                    log.info("CallBackController.loadCache,dingEnterpriseResultResult={}", dingEnterpriseResultResult);
//                    DingEnterpriseResult result = dingEnterpriseResultResult.getData();
//                    String token = DingRequestUtil.getToken(result.getClientIp(), result.getAppKey(),
//                            result.getAppSecret());
//                    log.info("CallBackController.loadCache,key={},token={}", key, token);
//                    return token;
//                }
//            });
//
//    // 钉钉企业绑定时，需要注册的回调接口
//    // 钉钉员工变动都会调用该接口
//    @RequestMapping(value = "/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public String callback(@RequestParam(value = "signature", required = false) String signature,
//                           @RequestParam(value = "timestamp", required = false) String timestamp,
//                           @RequestParam(value = "nonce", required = false) String nonce,
//                           @RequestParam(value = "ei", required = false) Integer ei, @RequestBody(required = false) JSONObject json) {
//
//        log.info("callback:param signature:{},timestamp:{},nonce:{},ei:{},json:{}", signature, timestamp, nonce, ei,
//                json);
//
//        try {
//            if (Objects.isNull(ei)) {
//                log.warn("param ei is null");
//                return CALLBACK_RESPONSE_FAIL;
//            }
//            Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEi(ei);
//            log.info("CallBackController.callback,queryEnterpriseByEi,result={}", result);
//            if (Objects.isNull(result) || Objects.isNull(result.getData())) {
//                log.warn("the fx enterprise is not binded, ei={}.", ei);
//                return CALLBACK_RESPONSE_FAIL;
//            }
//            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
//                    result.getData().getDingCorpId());
//            // 从post请求的body中获取回调信息的加密数据进行解密处理
//            String encryptMsg = json.getString("encrypt");
//            String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp, nonce, encryptMsg);
//            log.info("plainText={}", plainText);
//            JSONObject obj = JSON.parseObject(plainText);
//            Gson gson = new Gson();
//            // 根据回调数据类型做不同的业务处理
//            String eventType = obj.getString("EventType");
//            String corpId = obj.getString("CorpId");
//            // 匹配事件類型
//            // 请求客户的服务器有时候会比较慢，需要异步请求
//            switch (eventType) {
//                case ADD_USER:
//                    List<String> AddUserIds = gson.fromJson(obj.getString("UserId"), List.class);
//                    log.info("新增钉钉员工，ei:{},userId={}.", ei, obj.getString("UserId"));
//                    objectMappingService.updateEmp(corpId, AddUserIds);
//                    break;
//                case MODIFY_USER:
//                    List<String> modifyUserIds = gson.fromJson(obj.getString("UserId"), List.class);
//                    log.info("钉钉员工发生修改，ei:{},userId={}.", ei, obj.getString("UserId"));
//                    objectMappingService.updateEmp(corpId, modifyUserIds);
//                    break;
//                case LEAVE_USER:
//                    List<String> leaveUserIds = gson.fromJson(obj.getString("UserId"), List.class);
//                    log.info("钉钉员工离职，ei:{},userId={}.", ei, obj.getString("UserId"));
//                    objectMappingService.stopEmp(corpId, leaveUserIds);
//                    break;
//                // 部門
//                case CREATE_DEPT:
//                    List<Long> createDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
//                    }.getType());
//
//                    log.info("新增钉钉部门，ei:{},createDepts={}.", ei, createDepts);
//                    objectMappingService.createFxDept(corpId, createDepts);
//                    break;
//                case MODIFY_DEPT:
//                    List<Long> modifyDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
//                    }.getType());
//                    log.info("修改钉钉部门，ei:{},modifyDepts={}.", ei, modifyDepts);
//                    objectMappingService.modifyFxDept(corpId, modifyDepts);
//                    break;
//                case REMOVE_DEPT:
//                    List<Long> removeDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
//                    }.getType());
//                    log.info("刪除钉钉部门，ei:{},removeDepts={}.", ei, removeDepts);
//                    objectMappingService.removeFxDept(corpId, removeDepts);
//                    break;
//                default:
//                    break;
//            }
//
//            // 返回success的加密信息表示回调处理成功
//            Map<String, String> map = dingTalkEncryptor.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS,
//                    System.currentTimeMillis(), Utils.getRandomStr(8));
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.putAll(map);
//            return jsonObject.toString();
//        } catch (Exception e) {
//            log.warn("process callback failed，e={}", e);
//            return CALLBACK_RESPONSE_FAIL;
//        }
//        // 回调企业部门的变更事件
//    }
//
//    // 纷享的代办消息推送到钉钉时，会将该接口的url嵌入消息中，点击代办消息时会进入到该接口，钉钉点击代办
//    @RequestMapping(value = "/authorize", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Object authorize(@RequestParam(value = "code", required = false) String code,
//                            @RequestParam(value = "state", required = false) String state,
//                            @RequestParam(value = "apiname", required = false) String apiName,
//                            @RequestParam(value = "id", required = false) String dataId,
//                            @RequestParam(value = "instanceId", required = false) String instanceId,
//                            @RequestParam(value = "taskId", required = false) String taskId,
//                            @RequestParam(value = "ei", required = false) Integer ei,
//                            @RequestHeader(required = false, value = "User-Agent") String userAgent) {
//
//        log.info("message :{}" + code + state + apiName + dataId + instanceId + taskId + ei);
//
//        // 查询绑定企业
//        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEi(ei);
//        log.info("CallBackController.authorize,queryEnterpriseByEi,result={}", result);
//
//        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
//            log.warn("the fx enterprise is not binded, ei={}.", ei);
//            return CALLBACK_RESPONSE_FAIL;
//        }
//        String redirectAppId = result.getData().getRedirectAppId();
//        String redirectAppSecret = result.getData().getRedirectAppSecret();
//
//        Gson gson = new Gson();
//        Map<String, String> argMap = new HashMap<>();
//        argMap.put("redirectAppId", redirectAppId);
//        argMap.put("redirectAppSecret", redirectAppSecret);
//        argMap.put("token", result.getData().getToken());
//        argMap.put("code", code);
//        // 获取用户信息
//        String getUserUrl = result.getData().getClientIp() + "proxy/getUserByCode";
//        log.info("getUserUrl={}, userAgent={}", getUserUrl, userAgent);
//        CloseableHttpResponse response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
//        String entity = null;
//        try {
//            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
//            log.info("callBackController entity :{}", entity);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        OapiSnsGetuserinfoBycodeResponse.UserInfo dingUser = gson.fromJson(entity,
//                OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
//        // 存储DingUser到Redis，因为在点击代办消息的时候，安卓机器会触发两次redirect_url请求，code会失效会返回空的信息
//        log.info("dinguser callbackController:{}", dingUser);
//        if (!Objects.isNull(dingUser)) {
//            DingUserVo vo = BeanUtil.copy(dingUser, DingUserVo.class);
//            redisDingService.saveInfoToRedis(code, vo);
//        } else {
//            Result<DingUserVo> infos = redisDingService.getInfoFromRedis(code);
//            log.info("redisDingService getInfo:{}", infos.getData());
//            dingUser = BeanUtil.copy(infos.getData(), OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
//        }
//        log.info("redis info:{}", dingUser);
//        dingUser = Optional.ofNullable(dingUser).orElse(new OapiSnsGetuserinfoBycodeResponse.UserInfo());
//        String name = dingUser.getNick();
//        String openId = dingUser.getOpenid();
//        String unionId = dingUser.getUnionid();
//        log.info("dingUser:{},name={},openId={},unionId={}", dingUser, name, openId, unionId);
//        // 根据员工绑定关系获取纷享员工身份
//        Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmployeeByUnionId(ei, unionId);
//        if (Objects.isNull(mappingResult.getData()) || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())) {
//            log.info("the emp not bind");
//            return Result.newError(ResultCode.NOT_BIND_EMP);
//        }
//        Integer fsUserId = mappingResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(
//                new UserTokenDto(ei, Integer.valueOf(fsUserId)));
//        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
//        log.info("ssoLogin token :{}", ssoResult);
//        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
//        String fsToken = ssoResult.getToken();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].",
//                    ei, fsUserId, ssoResult);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].",
//                    ei, fsUserId, ssoResult);
//        }
//        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
//        log.info("redirectUrl[{}]", redirectUrl);
//        String sourceUrl = null;
//        String finalUrl = null;
//        if (userAgent.contains("AliApp")) {
//            if (!StringUtils.isEmpty(taskId) && BPM_URL_TYPE.equals(taskId)) {
//                sourceUrl = ssoSourceBpmH5Url + instanceId;// bpm业务流程详情页
//            } else {
//                sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;// 业务对象详情页
//            }
//            finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
//            log.info("finalUrl[{}]", finalUrl);
//            ModelAndView mv = new ModelAndView("redirect:" + finalUrl);
//            mv.addObject("message", "登录成功！");
//            return mv;
//        } else {
//            sourceUrl = ssoRedirectWebUrl + apiName + "/" + dataId;
//            finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
//        }
//        String url = DING_REDIRECT_URL + URLEncoder.encode(finalUrl);
//        log.info("SignOnManager redirectThirdRequest, ei[{}], url[{}]", ei, url);
//        ModelAndView mv = new ModelAndView("redirect:" + url);
//        mv.addObject("message", "登录成功！");
//        return mv;
//    }
//
//    // 点击H5应用图标，会跳转的接口。通过客户client的jsp地址，在jsp中会再次调用这个接口
//    @RequestMapping(value = "/authorizeByApp", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
//    @CrossOrigin(origins = "*", maxAge = 3600)
//    @ResponseBody
//    public Object authorizeByApp(@RequestParam(value = "code", required = false) String code,
//                                 @RequestParam(value = "corpId", required = false) String corpId,
//                                 @RequestHeader(required = false, value = "User-Agent") String userAgent,
//                                 HttpServletResponse httpServletResponse) throws IOException {
//        StopWatch stopWatch = StopWatch.create("dingtalk authorize by app" + corpId);
//
//        // 查询绑定企业
//        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByCorpId(corpId);
//        log.info("CallBackController.authorizeByApp,queryEnterpriseByEi,result={}", result);
//        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
//            log.warn("the fx enterprise is not binded, ei={}.", corpId);
//            return CALLBACK_RESPONSE_FAIL;
//        }
//        stopWatch.lap("ding getEnterprise");
//
//        // 获取token
//        String accessToken = cache.get(String.valueOf(result.getData().getEi()));
//        if (org.apache.commons.lang3.StringUtils.isEmpty(accessToken)) {
//            log.warn("startConnect appKey或appSecret错误.");
//            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
//        }
//        stopWatch.lap("ding getToken");
//
//        Gson gson = new Gson();
//        String getUserUrl = result.getData().getClientIp() + "proxy/proxyRequest";
//        log.info("getUserUrl={}, userAgent={}", getUserUrl, userAgent);
//        Map<String, Object> argMap = new HashMap<>();
//        argMap.put("type", "GET");
//        String getTokenUrl = DingUrl.GET_USER_BY_APP.concat("?access_token=").concat(accessToken).concat("&code=")
//                .concat(code);
//        argMap.put("url", getTokenUrl);
//        argMap.put("token", result.getData().getToken());
//        argMap.put("code", code);
//        CloseableHttpResponse response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
//        String entity = null;
//        try {
//            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        stopWatch.lap("ding getUser");
//        Integer errcode = ObjectUtils.isEmpty(JSONPath.read(entity, "$.errcode")) ? null
//                : Integer.parseInt(JSONPath.read(entity, "$.errcode").toString());
//        if (errcode.equals(DING_INVALID_ACCESSTOKEN)) {
//            // 重新获取access_token，刷新缓存
//            Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService
//                    .queryEnterpriseByEi(result.getData().getEi());
//            DingEnterpriseResult enterpriseResult = dingEnterpriseResultResult.getData();
//            String refreshToken = DingRequestUtil.getToken(enterpriseResult.getClientIp(), enterpriseResult.getAppKey(),
//                    enterpriseResult.getAppSecret());
//            cache.put(String.valueOf(result.getData().getEi()), refreshToken);
//            String refreshTokenUrl = DingUrl.GET_USER_BY_APP.concat("?access_token=").concat(refreshToken)
//                    .concat("&code=").concat(code);
//            argMap.put("url", refreshTokenUrl);
//            response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
//            try {
//                entity = EntityUtils.toString(response.getEntity(), "UTF-8");
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            log.info("trace refresh token url ei:{},accessToken:{},entity:{},", result.getData().getEi(),
//                    XorUtils.EncodeByXor(refreshToken, ConfigCenter.XOR_SECRET_KEY), entity);
//        }
//
//        DingUserInfo dingUser = gson.fromJson(entity, DingUserInfo.class);
//        String userId = dingUser.getUserid();
//        log.info("trace authorizeByApp by app ei:{},userId={}", corpId, userId);
//        // 根据员工绑定关系获取纷享员工身份
//        Result<DingMappingEmployeeResult> mappingResult = objectMappingService
//                .queryEmpByDingUserId(result.getData().getEi(), userId);
//        if (Objects.isNull(mappingResult) || Objects.isNull(mappingResult.getData())
//                || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())) {
//            log.info("the emp not bind");
//            return Result.newError(ResultCode.NOT_BIND_EMP);
//        }
//        Integer fsUserId = mappingResult.getData().getEmployeeId();
//        stopWatch.log();
//        stopWatch.lap("ding query dataBase user");
//
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(
//                new UserTokenDto(result.getData().getEi(), Integer.valueOf(fsUserId)));
//        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
//        stopWatch.lap("ding create sso token");
//
//        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
//        String fsToken = ssoResult.getToken();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn(
//                    "authorizeByApp SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].",
//                    corpId, fsUserId, ssoResult);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn(
//                    "authorizeByApp SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].",
//                    corpId, fsUserId, ssoResult);
//        }
//        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
//        log.info("authorizeByApp redirectUrl[{}]", redirectUrl);
//        if (userAgent.contains("AliApp")) {
//            redirectUrl = redirectUrl.concat("&source=").concat(h5Url);
//            log.info("aliapp:{},redirectUrl:{}", userAgent, redirectUrl);
//        }
//
//        log.info("authorizeByApp SignOnManager redirectThirdRequest, ei[{}], url[{}]", corpId, redirectUrl);
//        Map<String, String> map = new HashMap<>();
//        map.put("redirectUrl", redirectUrl);
//        httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
//        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST");
//        httpServletResponse.setHeader("Content-Type", "application/javascript");
//        stopWatch.log();
//        return JSONObject.toJSONString(map);
//
//    }
//
//    public static String sign(String ticket, String nonceStr, long timeStamp, String url) {
//        try {
//            return DingTalkJsApiSingnature.getJsApiSingnature(url, nonceStr, timeStamp, ticket);
//        } catch (Exception ex) {
//            log.info("sign failed");
//            return null;
//        }
//    }
//
//    /**
//     * 指定部门创建
//     */
//    @RequestMapping(value = "/createDept", method = RequestMethod.POST)
//    public Result<Void> createDept(@RequestParam("corpId") String corpId, @RequestBody List<Long> ids) {
//        UserVo userVo = getUserVo();
//        log.info("createDept start ....");
//        objectMappingService.createFxDept(corpId, ids);
//
//        return new Result<>();
//    }
//
//}
