//package com.facishare.open.ding.cloud.paas.constants;
//
//public enum EventTypeEnum {
//    ADD(1, "新增", "syncdata_common_eventypeenum_add"),
//    UPDATE(2, "修改", "syncdata_common_eventypeenum_update"),
//    INVALID(3, "作废", "syncdata_common_eventypeenum_invalid"),
//    SYNC_MENU(4, "手动同步", "syncdata_common_eventypeenum_sync_menu"),
//    DELETE_DIRECT(5, "直接删除", "syncdata_common_eventypeenum_delete_direct"),
//    HAS_MAPPING_NO_CHECK_SYNC_CONDITION(6, "存在映射，则不检查数据范围", "syncdata_common_eventypeenum_has_mapping_no_check_sync_condition"),
//    NOT_IN_SYNC_DATA_MAPPING_AND_SCOPE_NOT_ADD(7, "更新字段不在字段映射或数据范围中则不新增", "syncdata_common_eventypeenum_not_in_sync_data_mapping_and_scope_not_update"),
//    WAITTING(100, "等待", "syncdata_common_eventypeenum_waitting"),
//    INIT(101, "初始化", "syncdata_common_eventypeenum_init"),
//    DEPEND(102, "依赖处理", "syncdata_common_eventypeenum_depend"),
//    ;
//    private int type;
//    private String name;
//    private String nameI18nKey;
//
//    EventTypeEnum(int type, String name, String nameI18nKey) {
//        this.type = type;
//        this.name = name;
//        this.nameI18nKey = nameI18nKey;
//    }
//
//    public int getType() {
//        return type;
//    }
//
//    public String getName() {
//        return this.name;
//    }
//
//    public static String getNameByType(int type) {
//        for (EventTypeEnum eventType : EventTypeEnum.values()) {
//            if (eventType.getType() == type) {
//                return eventType.getName();
//            }
//        }
//        return null;
//    }
//}
