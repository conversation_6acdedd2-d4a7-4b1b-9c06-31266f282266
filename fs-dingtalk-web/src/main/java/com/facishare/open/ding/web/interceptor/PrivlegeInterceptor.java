package com.facishare.open.ding.web.interceptor;

import com.facishare.open.ding.web.utils.WebUtils;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.http.conn.util.InetAddressUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * IP 初始化拦截器
 * @dateTime 2018/7/17 14:59
 * @<NAME_EMAIL>
 * @version 1.0
 */
public class PrivlegeInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(PrivlegeInterceptor.class);
    
    private static final String ipHeaderName = "x-forwarded-for";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            WebUtils.clear();
            String ip = getAddress(request);
            WebUtils.setReqIP(ip);
            WebUtils.setReqURI(request.getRequestURI());
        } catch (Exception e) {
            logger.error("PrivlegeInterceptor setIP Exception {}", e);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
    }
    
    private String getAddress(HttpServletRequest request) {

        if (ipHeaderName == null) {
            return extractRemoteAddress(request);
        }
        String headerIp = request.getHeader(ipHeaderName);
        logger.info("PrivlegeInterceptor Request HeadlerIp={}", headerIp);
        
        if (headerIp != null) {
            //处理多级proxy的情况
            headerIp = headerIp.split(",")[0].trim();
            if (InetAddressUtils.isIPv4Address(headerIp)) {
                return headerIp;
            }
        }
        return extractRemoteAddress(request);

    }

    private static String extractRemoteAddress(HttpServletRequest request) {
        String remoteIp = request.getRemoteAddr();
        logger.info("PrivlegeInterceptor Request RemoteIp={}", remoteIp);
        
        if (InetAddressUtils.isIPv4Address(remoteIp)) {
            return remoteIp;
        }
        return "";
    }
    
}
