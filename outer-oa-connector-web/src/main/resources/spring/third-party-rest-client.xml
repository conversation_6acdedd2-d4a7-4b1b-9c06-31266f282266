<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="dingTalkIsvApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-open-dingtalk-all"/>
    </bean>
    <bean id="dingTalkIsvService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.web.thirdparty.auth.DingAuthService"/>
        <property name="serverHostProfile" ref="dingTalkIsvApiHostProfile"/>
    </bean>
    <bean id="dingTalkIsvOuterService" class="com.facishare.open.outer.oa.connector.common.api.outerInterface.factory.OuterServiceDubboRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"/>
        <property name="serverHostProfile" ref="dingTalkIsvApiHostProfile"/>
        <property name="channelEnums">
            <list>
                <value>dingding</value>
            </list>
        </property>

        <property name="typeEnums">
            <list>
                <value>isv</value>
            </list>
        </property>
    </bean>

    <bean id="dingTalkSelfBuildApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-dingtalk-web"/>
    </bean>
    <bean id="dingTalkSelfBuildOuterService" class="com.facishare.open.outer.oa.connector.common.api.outerInterface.factory.OuterServiceDubboRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"/>
        <property name="serverHostProfile" ref="dingTalkSelfBuildApiHostProfile"/>
        <property name="channelEnums">
            <list>
                <value>dingding</value>
            </list>
        </property>
        <property name="typeEnums">
            <list>
                <value>selfBuild</value>
            </list>
        </property>
    </bean>

    <bean id="qyweixinApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="qywx-outer-config"/>
    </bean>
    <bean id="qyweixinService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.web.thirdparty.auth.QyweixinAccountSyncService"/>
        <property name="serverHostProfile" ref="qyweixinApiHostProfile"/>
    </bean>
    <bean id="qyweixinOuterService" class="com.facishare.open.outer.oa.connector.common.api.outerInterface.factory.OuterServiceDubboRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"/>
        <property name="serverHostProfile" ref="qyweixinApiHostProfile"/>
        <property name="channelEnums">
            <list>
                <value>qywx</value>
            </list>
        </property>

    </bean>


    <bean id="feishuApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-feishu-config"/>
    </bean>
    <bean id="feishuService" class="com.facishare.open.outer.oa.connector.web.thirdparty.overwrite.GetRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.web.thirdparty.auth.FeishuAuthService"/>
        <property name="serverHostProfile" ref="feishuApiHostProfile"/>
    </bean>

    <bean id="feishuOuterService" class="com.facishare.open.outer.oa.connector.common.api.outerInterface.factory.OuterServiceDubboRestFactoryBean">
        <property name="objectType"
                  value="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"/>
        <property name="serverHostProfile" ref="feishuApiHostProfile"/>
        <property name="channelEnums">
            <list>
                <value>feishu</value>
                <value>lark</value>
            </list>
        </property>
    </bean>

<!--    <bean id="larkApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">-->
<!--        <property name="configName" value="fs-feishu-config"/>-->
<!--    </bean>-->
<!--&lt;!&ndash;    <bean id="larkService" class="com.facishare.open.outer.oa.connector.web.thirdparty.overwrite.GetRestFactoryBean">&ndash;&gt;-->
<!--&lt;!&ndash;        <property name="objectType"&ndash;&gt;-->
<!--&lt;!&ndash;                  value="com.facishare.open.outer.oa.connector.web.thirdparty.auth.FeishuAuthService"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <property name="serverHostProfile" ref="feishuApiHostProfile"/>&ndash;&gt;-->
<!--&lt;!&ndash;    </bean>&ndash;&gt;-->

<!--    <bean id="larkOuterService" class="com.facishare.open.outer.oa.connector.common.api.outerInterface.factory.OuterServiceDubboRestFactoryBean">-->
<!--        <property name="objectType"-->
<!--                  value="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"/>-->
<!--        <property name="serverHostProfile" ref="feishuApiHostProfile"/>-->
<!--        <property name="channelEnum" value="lark"/>-->
<!--    </bean>-->
</beans>


