package com.facishare.open.outer.oa.connector.web.controller.cep;

import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.web.exception.OutOaConnectorException;
import com.facishare.open.outer.oa.connector.web.model.CreateJsapiSignature;
import com.facishare.open.outer.oa.connector.web.model.JsapiSignature;
import com.facishare.open.outer.oa.connector.web.service.OuterChannelAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:19:15
 */
@RestController
@RequestMapping("/out/oa")
public class OutOaChannelController {

    @Autowired
    private Map<String, OuterChannelAuthService> authServiceMap;

    @PostMapping("/{channel}/signature")
    public JsapiSignature createJsapiSignature(@PathVariable("channel") String channel, @RequestBody CreateJsapiSignature.Arg arg) {
        final OuterChannelAuthService authService = authServiceMap.get(channel);
        if (Objects.isNull(authService)) {
            throw new OutOaConnectorException(I18NStringEnum.cxb001, channel);
        }

        return authService.getJsapiSignature(arg.getAppId(), arg.getFsEa(), arg.getOutEa(), arg.getUrl());
    }
}
