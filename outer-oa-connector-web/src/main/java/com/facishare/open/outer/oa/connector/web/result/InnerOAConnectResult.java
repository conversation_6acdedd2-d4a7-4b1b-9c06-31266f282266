package com.facishare.open.outer.oa.connector.web.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InnerOAConnectResult implements Serializable {
    private String channel;
    private String id;
    private Boolean hasConnect;
    private Boolean hasErpChannel;
    private Boolean hasInited;
    private String connectorName;
    private String dataCenterName;
    public String connectorType;
    private boolean newOuterOAPage=true;

}
