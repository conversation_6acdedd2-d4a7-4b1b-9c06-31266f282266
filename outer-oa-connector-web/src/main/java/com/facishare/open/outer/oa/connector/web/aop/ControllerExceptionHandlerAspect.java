package com.facishare.open.outer.oa.connector.web.aop;

import com.facishare.cep.plugin.exception.BizException;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.open.outer.oa.connector.web.exception.OutOaConnectorException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/15 17:38:12
 */
@Component
@Aspect
@Slf4j
public class ControllerExceptionHandlerAspect {

    @Autowired
    private I18NStringManager i18NStringManager;

    @AfterThrowing(pointcut = "execution(* com.facishare.open.outer.oa.connector.web.controller.cep..*(..))", throwing = "e")
    public void CepOutOaConnectorException(JoinPoint joinPoint, OutOaConnectorException e) {
        // 获取目标类名
        String className = joinPoint.getTarget().getClass().getSimpleName();
        // 获取目标方法名
        String methodName = joinPoint.getSignature().getName();
        log.warn("{}-{} error. ", className, methodName, e);

        // cep的都有locale和ei, todo: failureCode
        throw new BizException(i18NStringManager.getByTraceContext(e.getErrorEnum(), e.getErrorParam()), 1234);
    }


    @AfterThrowing(pointcut = "execution(* com.facishare.open.outer.oa.connector.web.controller..*(..)) && !execution(* com.facishare.open.outer.oa.connector.web.controller.cep..*(..))", throwing = "e")
    public void OutOaConnectorException(JoinPoint joinPoint, OutOaConnectorException e) {
        // 获取目标类名
        String className = joinPoint.getTarget().getClass().getSimpleName();
        // 获取目标方法名
        String methodName = joinPoint.getSignature().getName();
        log.warn("{}-{} error. ", className, methodName, e);
    }
}
