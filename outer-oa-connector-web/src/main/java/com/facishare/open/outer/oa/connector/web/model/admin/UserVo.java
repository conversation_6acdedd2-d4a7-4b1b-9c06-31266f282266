package com.facishare.open.outer.oa.connector.web.model.admin;

import com.facishare.asm.api.auth.AuthXC;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;


@Data
public class UserVo implements Serializable {
    private static final long serialVersionUID = -4270977122695909179L;
    private String enterpriseAccount;
    private Integer enterpriseId;
    private Integer employeeId;
    /**
     * 可能为空，当调用com.fxiaoke.open.erpsyncdata.web.controller.BaseController#getPhone方法时
     * 会再查询一次
     */
    private String mobile;
    /**
     * 数据中心id
     */
    private String dataCenterId;
    public UserVo(){
    }

    public UserVo(AuthXC authXC) {
        this.enterpriseAccount = authXC.getEnterpriseAccount();
        this.enterpriseId = authXC.getEnterpriseId();
        this.employeeId = authXC.getEmployeeId();
        this.mobile = authXC.getMobile();
    }

    public boolean isInvalid(){
        if(StringUtils.isBlank(enterpriseAccount)){
            return true;
        }
        if(employeeId==null||employeeId<=0){
            return true;
        }
        return false;
    }
}
