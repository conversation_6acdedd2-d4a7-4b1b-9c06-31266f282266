package com.facishare.open.oa.base.dbproxy.pg.entity.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 员工基础信息DTO
 */
@Data
public class EmployeeBaseInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 人员姓名
     */
    private String name;

    /**
     * 人员手机号
     */
    private String phone;

    /**
     * 人员邮箱
     */
    private String email;

    /**
     * 人员编号
     */
    private String employeeNumber;

    /**
     * 主属部门
     */
    private String mainDepartment;

    /**
     * 人员状态 (0:离职 1:在职)
     */
    private Integer status;
}