package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.oa.base.dbproxy.configVo.AutoEmployeeFiledBindVo;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeDataParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.enums.CRMEmployeeFiledEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.object.BaseOuterEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldResult;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Manager 类 - 外部OA人员信息
 */
@Component
// IgnoreI18nFile
public class OuterOaEmployeeDataManager {

    @Resource
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    // 字段名到Field对象的缓存
    private static final Map<String, Field> FIELD_CACHE = new ConcurrentHashMap<>();

    public Integer insert(OuterOaEmployeeDataEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaEmployeeDataMapper.insert(entity);
    }

    public Integer updateById(OuterOaEmployeeDataEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            return 0;
        }
        return outerOaEmployeeDataMapper.updateById(entity);
    }

    // deleteByUserId
    public Integer deleteByUserId(ChannelEnum channel, String outEa, String appId, String outUserId) {
        LambdaQueryWrapper<OuterOaEmployeeDataEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeDataEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEmployeeDataEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeDataEntity::getAppId, appId);
        wrapper.eq(OuterOaEmployeeDataEntity::getOutUserId, outUserId);
        return outerOaEmployeeDataMapper.delete(wrapper);
    }

    /**
     * 根据外部OA字段查询员工数据
     */
    public List<OuterOaEmployeeDataEntity> selectByField(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            String outerOAFieldKey, String outerOAFieldValue) {
        // 获取配置
        OuterOaConfigInfoEntity configEntity = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, outerOaEnterpriseBindEntity.getId());

        if (configEntity == null) {
            LogUtils.info("not query enterprise data:{}", configEntity);
            return null;
        }

        // 获取字段映射配置
        String dbField = getDbFieldFromConfig(configEntity.getConfigInfo(), outerOAFieldKey);
        if (dbField == null) {
            LogUtils.info("not query employee field:{}", outerOAFieldKey);
            return null;
        }

        // 创建查询参数并设置基础信息
        OuterOaEmployeeDataParams params = OuterOaEmployeeDataParams.builder()
                .channel(outerOaEnterpriseBindEntity.getChannel()).outEa(outerOaEnterpriseBindEntity.getOutEa())
                .appId(configEntity.getAppId()).build();

        try {
            // 通过反射设置字段值
            Field field = OuterOaEmployeeDataParams.class.getDeclaredField(dbField);
            field.setAccessible(true);
            field.set(params, outerOAFieldValue);

            return getEntities(params);
        } catch (Exception e) {
            LogUtils.error("反射设置字段失败: field={}, value={}", dbField, outerOAFieldValue, e); // ignoreI18n
            return null;
        }
    }

    /**
     * 从配置中获取数据库字段名
     */
    private String getDbFieldFromConfig(String configInfo, String outerOAFieldKey) {
        if (StringUtils.isEmpty(configInfo) || StringUtils.isEmpty(outerOAFieldKey)) {
            return null;
        }
        try {
            SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(configInfo,SystemFieldMappingResult.class);
            return systemFieldResult.getItemFieldMappings().stream().filter(bindVo -> outerOAFieldKey.equals(bindVo.getOuterOAFieldApiName()))
                    .map(SystemFieldMappingResult.ItemFieldMapping::getOuterDataText).findFirst().orElse(null);
        } catch (Exception e) {
            LogUtils.error("expire filed", e); // ignoreI18n
            return null;
        }
    }

    /**
     * 根据参数查询人员信息列表
     *
     * @param params 查询参数
     * @return 人员信息实体列表
     */
    public List<OuterOaEmployeeDataEntity> getEntities(OuterOaEmployeeDataParams params) {
        LambdaQueryWrapper<OuterOaEmployeeDataEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getOutEa, params.getOutEa());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaEmployeeDataEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getOutUserId())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getOutUserId, params.getOutUserId());
        }
        if (StringUtils.isNotEmpty(params.getOutDeptId())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getOutDeptId, params.getOutDeptId());
        }
        if (StringUtils.isNotEmpty(params.getText1())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getText1, params.getText1());
        }
        if (StringUtils.isNotEmpty(params.getText2())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getText2, params.getText2());
        }
        if (StringUtils.isNotEmpty(params.getText3())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getText3, params.getText3());
        }
        if (StringUtils.isNotEmpty(params.getText4())) {
            wrapper.eq(OuterOaEmployeeDataEntity::getText4, params.getText4());
        }

        return outerOaEmployeeDataMapper.selectList(wrapper);
    }

    /**
     * 批量更新外部OA人员信息 根据channel、appId、outEa、outUserId作为唯一键 不存在则插入，存在则更新其他字段
     *
     * @param entities 需要更新的实体列表
     * @return 更新成功的记录数
     */
    @Deprecated
    public Integer batchUpdateOutUserId(List<OuterOaEmployeeDataEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 过滤出有效的实体
        List<OuterOaEmployeeDataEntity> validEntities = entities.stream()
                .filter(entity -> !StringUtils.isEmpty(entity.getAppId()) && !StringUtils.isEmpty(entity.getOutEa())
                        && entity.getChannel() != null && !StringUtils.isEmpty(entity.getOutUserId()))
                .collect(Collectors.toList());

        if (validEntities.isEmpty()) {
            LogUtils.warn("No valid entities found for batch update");
            return 0;
        }

        try {
            // 确保每个实体都有updateTime
            long now = System.currentTimeMillis();
            validEntities.forEach(entity -> {
                if (entity.getUpdateTime() == null) {
                    entity.setUpdateTime(now);
                }
            });

            // 按唯一键去重，保留最后一个实体（通常最新的更新）
            Map<String, OuterOaEmployeeDataEntity> uniqueEntities = new LinkedHashMap<>();
            for (OuterOaEmployeeDataEntity entity : validEntities) {
                // 创建唯一键：channel_appId_outEa_outUserId
                String uniqueKey = entity.getChannel() + "_" + entity.getAppId() + "_" + entity.getOutEa() + "_" + entity.getOutUserId();
                uniqueEntities.put(uniqueKey, entity);
            }
            
            // 将去重后的实体转换回列表
            List<OuterOaEmployeeDataEntity> deduplicatedEntities = new ArrayList<>(uniqueEntities.values());
            
            // 如果没有实体，直接返回
            if (deduplicatedEntities.isEmpty()) {
                LogUtils.warn("No valid entities after deduplication");
                return 0;
            }
            
            LogUtils.info("Batch save or update: original count={}, deduplicated count={}", 
                         validEntities.size(), deduplicatedEntities.size());

            // 执行批量插入或更新
            return outerOaEmployeeDataMapper.batchSaveOrUpdate(deduplicatedEntities);
        } catch (Exception e) {
            LogUtils.error("Failed to execute batch save or update: {}", e.getMessage(), e);
            return 0;
        }
    }

    // selectCount
    public Long selectCount(ChannelEnum channel, String outEa, String appId) {
        LambdaQueryWrapper<OuterOaEmployeeDataEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeDataEntity::getOutEa, outEa).eq(OuterOaEmployeeDataEntity::getChannel, channel)
                .eq(OuterOaEmployeeDataEntity::getAppId, appId);
        return outerOaEmployeeDataMapper.selectCount(wrapper);
    }

    /**
     * 查询指定条件下的所有员工ID
     *
     * @param outEa   外部企业账号
     * @param channel 渠道
     * @param appId   应用ID
     * @return 员工ID列表
     */
    public List<String> queryAllUserIds(String outEa, ChannelEnum channel, String appId) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId)) {
            LogUtils.warn("Invalid parameters for queryAllUserIds: outEa={}, channel={}, appId={}", outEa, channel,
                    appId);
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<OuterOaEmployeeDataEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                    .eq(OuterOaEmployeeDataEntity::getChannel, channel).eq(OuterOaEmployeeDataEntity::getAppId, appId)
                    .select(OuterOaEmployeeDataEntity::getOutUserId); // 只查询 outUserId 字段

            return outerOaEmployeeDataMapper.selectList(queryWrapper).stream()
                    .map(OuterOaEmployeeDataEntity::getOutUserId).collect(Collectors.toList());
        } catch (Exception e) {
            LogUtils.error("Failed to query user ids: outEa={}, channel={}, appId={}, error={}", outEa, channel, appId,
                    e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询指定条件下的所有员工ID，并删除不在可见范围内的记录
     *
     * @param outEa          外部企业账号
     * @param channel        渠道
     * @param appId          应用ID
     * @param visibleUserIds 可见的员工ID集合
     * @return 删除的记录数
     */
    public Integer deleteInvisibleUsers(String outEa, ChannelEnum channel, String appId, Set<String> visibleUserIds) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId)) {
            LogUtils.warn("Invalid parameters for deleteInvisibleUsers: outEa={}, channel={}, appId={}", outEa, channel,
                    appId);
            return 0;
        }

        try {
            // 1. 查询数据库中现有的所有员工ID
            List<String> existingUserIds = queryAllUserIds(outEa, channel, appId);
            Set<String> userIdsToDelete = new HashSet<>(existingUserIds);

            // 2. 计算需要删除的员工ID（差集）
            userIdsToDelete.removeAll(visibleUserIds);
            if (userIdsToDelete.isEmpty()) {
                LogUtils.info("No users need to be deleted for outEa={}, channel={}, appId={}", outEa, channel, appId);
                return 0;
            }

            // 3. 删除不可见的员工记录
            LambdaQueryWrapper<OuterOaEmployeeDataEntity> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                    .eq(OuterOaEmployeeDataEntity::getChannel, channel).eq(OuterOaEmployeeDataEntity::getAppId, appId)
                    .in(OuterOaEmployeeDataEntity::getOutUserId, userIdsToDelete);

            int deletedCount = outerOaEmployeeDataMapper.delete(deleteWrapper);
            LogUtils.info("Deleted {} invisible users for outEa={}, channel={}, appId={}", deletedCount, outEa, channel,
                    appId);
            return deletedCount;
        } catch (Exception e) {
            LogUtils.error("Failed to delete invisible users: outEa={}, channel={}, appId={}, error={}", outEa, channel,
                    appId, e.getMessage(), e);
            return 0;
        }
    }

    public OuterOaEmployeeDataEntity queryByChannelAndAppIdAndEmpId(ChannelEnum channel, String appId, String outEa,
            String empId) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId) || StringUtils.isEmpty(empId)) {
            LogUtils.warn(
                    "Invalid parameters for queryByChannelAndAppIdAndEmpId: outEa={}, channel={}, appId={}, empId={}",
                    outEa, channel, appId, empId);
            return null;
        }
        LambdaQueryWrapper<OuterOaEmployeeDataEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaEmployeeDataEntity::getChannel, channel).eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                .eq(OuterOaEmployeeDataEntity::getAppId, appId).eq(OuterOaEmployeeDataEntity::getOutUserId, empId);
        return outerOaEmployeeDataMapper.selectOne(queryWrapper);
    }

    /**
     * 根据渠道、应用ID和员工ID列表查询员工数据
     *
     * @param channel 渠道枚举
     * @param appId   应用ID
     * @param empIds  员工ID列表
     * @return 员工数据实体列表
     */
    public List<OuterOaEmployeeDataEntity> queryByChannelAndAppIdAndEmpIds(String outEa, ChannelEnum channel,
            String appId, List<String> empIds) {
        if (channel == null || StringUtils.isEmpty(appId) || empIds == null || empIds.isEmpty()) {
            LogUtils.warn(
                    "Invalid parameters for queryByChannelAndAppIdAndEmpIds: channel={}, appId={}, empIds size={}",
                    channel, appId, empIds == null ? 0 : empIds.size());
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<OuterOaEmployeeDataEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OuterOaEmployeeDataEntity::getChannel, channel)
                    .eq(OuterOaEmployeeDataEntity::getOutEa, outEa).eq(OuterOaEmployeeDataEntity::getAppId, appId)
                    .in(OuterOaEmployeeDataEntity::getOutUserId, empIds);

            List<OuterOaEmployeeDataEntity> result = outerOaEmployeeDataMapper.selectList(queryWrapper);
            LogUtils.info("Successfully queried employee data: channel={}, appId={}, empIds size={}, result size={}",
                    channel, appId, empIds.size(), result.size());
            return result;
        } catch (Exception e) {
            LogUtils.error("Failed to query employee data: channel={}, appId={}, empIds size={}, error={}", channel,
                    appId, empIds.size(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询未绑定的外部员工数据（分页）
     *
     * @param outEa    外部企业账号
     * @param channel  渠道
     * @param appId    应用ID
     * @param pageNo   页码（从1开始）
     * @param pageSize 每页大小
     * @param keyword  搜索关键词(可选)
     * @return 分页结果
     */
    public IPage<OuterOaEmployeeDataEntity> queryUnboundEmployees(String outEa, ChannelEnum channel, String appId,
            Integer pageNo, Integer pageSize, String keyword) {

        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId)) {
            LogUtils.warn("Invalid parameters for queryUnboundEmployees: outEa={}, channel={}, appId={}", outEa,
                    channel, appId);
            return new Page<>(pageNo, pageSize);
        }
        try {
            // 创建分页对象（MyBatis Plus的分页对象，页码从1开始）
            Page<OuterOaEmployeeDataEntity> page = new Page<>(pageNo, pageSize);
            // 调用Mapper执行查询
            IPage<OuterOaEmployeeDataEntity> result = outerOaEmployeeDataMapper.queryUnboundEmployees(page, outEa,
                    channel, appId, keyword);
            LogUtils.info(
                    "Successfully queried unbounded employee data: outEa={}, channel={}, appId={}, page={}, size={}, total={}",
                    outEa, channel, appId, pageNo, pageSize, result.getTotal());
            return result;
        } catch (Exception e) {
            LogUtils.error("Failed to query unbounded employee data: outEa={}, channel={}, appId={}, page={}, error={}",
                    outEa, channel, appId, pageNo, e.getMessage(), e);
            return new Page<>(pageNo, pageSize);
        }
    }

    /**
     * 查询未绑定的外部员工数据（分页，无搜索条件）
     *
     * @param outEa    外部企业账号
     * @param channel  渠道
     * @param appId    应用ID
     * @param pageNo   页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public IPage<OuterOaEmployeeDataEntity> queryUnboundEmployees(String outEa, ChannelEnum channel, String appId,
            Integer pageNo, Integer pageSize) {
        return queryUnboundEmployees(outEa, channel, appId, pageNo, pageSize, null);
    }

    /**
     * 根据外部OA字段查询未绑定的员工数据（分页）
     *
     * @param outerOaEnterpriseBindEntity 外部OA企业绑定实体
     * @param pageNo                      页码（从1开始）
     * @param pageSize                    每页大小
     * @param outerOAFieldKey             外部OA字段键
     * @param outerOAFieldValue           外部OA字段值
     * @return 分页结果
     */
    public IPage<OuterOaEmployeeDataEntity> queryUnboundEmployeesByOuterField(
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, Integer pageNo, Integer pageSize,
            String outerOAFieldKey, String outerOAFieldValue) {
        // 获取配置
        OuterOaEmployeeDataParams outerOaEmployeeDataParams = OuterOaEmployeeDataParams.builder()
                .fsEa(outerOaEnterpriseBindEntity.getFsEa())
                .appId(outerOaEnterpriseBindEntity.getAppId()).outEa(outerOaEnterpriseBindEntity.getOutEa())
                .channel(outerOaEnterpriseBindEntity.getChannel()).build();
         if(StringUtils.isNotEmpty(outerOAFieldKey)){
             OuterOaConfigInfoEntity configEntity = outerOaConfigInfoManager.getEntityByDataCenterId(
                     OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, outerOaEnterpriseBindEntity.getId());

             if (configEntity == null) {
                 LogUtils.info("not query enterprise config:{}", outerOaEnterpriseBindEntity.getId());
                 return new Page<>(pageNo, pageSize);
             }

             // 获取字段映射配置
             String dbField = getDbFieldFromConfig(configEntity.getConfigInfo(), outerOAFieldKey);
            if(dbField!=null){
                try {
                    // 通过反射设置字段值
                    Field field = OuterOaEmployeeDataParams.class.getDeclaredField(dbField);
                    field.setAccessible(true);
                    field.set(outerOaEmployeeDataParams, outerOAFieldValue);
                } catch (Exception e) {
                    LogUtils.error("反射设置字段失败: field={}, value={}", dbField, outerOAFieldValue, e);
                    return null;
                }
            }
         }
        IPage<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntityIPage = queryUnboundEmployeesByField(outerOaEmployeeDataParams, pageNo, pageSize);
         return outerOaEmployeeDataEntityIPage;
    }

    /**
     * 批量插入外部员工数据，需要根据配置文件，设置text的字段
     *
     * @param <T>           继承自BaseOuterEmployeeObject的泛型类型
     * @param outerDataList 外部OA人员数据列表
     * @param channelEnum   渠道枚举
     * @param dataCenterId  数据中心ID
     * @return 成功插入或更新的记录数
     */
    public <T extends BaseOuterEmployeeObject> Integer batchUpsert(List<T> outerDataList, ChannelEnum channelEnum,
            String dataCenterId) {
        if (outerDataList == null || outerDataList.isEmpty() || channelEnum == null
                || StringUtils.isEmpty(dataCenterId)) {
            LogUtils.warn("Invalid parameters for batchUpsert: dataList size={}, channel={}, dataCenterId={}",
                    outerDataList == null ? 0 : outerDataList.size(), channelEnum, dataCenterId);
            return 0;
        }
        try {
            // 获取配置信息
            OuterOaConfigInfoEntity configEntity = outerOaConfigInfoManager
                    .getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, dataCenterId);

            if (configEntity == null) {
                LogUtils.warn("Config not found for dataCenterId: {}", dataCenterId);
                return 0;
            }
            SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(configEntity.getConfigInfo(),
                    SystemFieldMappingResult.class);

            // 解析配置，建立outerOA字段到text字段的映射
            Map<String, String> outerEmployeeFieldMap = new HashMap<>();
            systemFieldResult.getItemFieldMappings().forEach(itemFieldMapping -> {
                if (itemFieldMapping.getOuterDataText() != null) {
                    outerEmployeeFieldMap.put(itemFieldMapping.getOuterOAFieldApiName(),
                            itemFieldMapping.getOuterDataText());
                }
            });

            // 获取绑定实体信息(用于获取outEa和appId)
            OuterOaEnterpriseBindEntity bindEntity = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
            if (bindEntity == null) {
                LogUtils.warn("Enterprise bind entity not found for dataCenterId: {}", dataCenterId);
                return 0;
            }

            // 准备要插入的实体数据
            List<OuterOaEmployeeDataEntity> entities = new ArrayList<>(outerDataList.size());
            long now = System.currentTimeMillis();

            // 获取渠道对应的员工对象类
            Class<?> employeeObjectClass = channelEnum.getEmployeeObjectClass();
            if (employeeObjectClass == null) {
                LogUtils.warn("No employee object class defined for channel: {}", channelEnum);
                return 0;
            }

            // 预处理：获取字段映射信息并缓存
            Map<String, Field> fieldCache = getFieldCache(employeeObjectClass);
            Field userIdField = getUserIdField(employeeObjectClass);
            // 获取部门字段
            Field deptField = getDepartmentField(employeeObjectClass);

            // 遍历处理每个员工数据
            for (T outerData : outerDataList) {
                OuterOaEmployeeDataEntity entity = new OuterOaEmployeeDataEntity();
                entity.setId(IdGenerator.get());
                entity.setOutEa(bindEntity.getOutEa());
                entity.setChannel(channelEnum);
                entity.setAppId(bindEntity.getAppId());
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                // 设置基本属性
                entity.setOutUserInfo(JSONObject.parseObject(JSONObject.toJSONString(outerData)));

                // 从源对象中获取USER_ID字段的值并设置
                String userId = getFieldValueAsString(outerData, userIdField);
                if (StringUtils.isNotEmpty(userId)) {
                    entity.setOutUserId(userId);
                } else {
                    // 如果没有找到USER_ID，则使用基类的ID
                    entity.setOutUserId(outerData.getId());
                }

                // 设置text字段 (text1-text4)
                mapFieldsToTextFields(entity, outerData, fieldCache, outerEmployeeFieldMap);

                // 设置部门ID
                setDepartmentId(entity, outerData, deptField);

                entities.add(entity);
            }

            // 执行批量插入或更新
            return batchUpdateOutUserId(entities);
        } catch (Exception e) {
            LogUtils.error("Failed to batch upsert employee data: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 批量插入或更新员工数据（分批处理）
     * 将大量数据分批次处理，降低单次操作的数据量
     *
     * @param entities 需要插入或更新的实体列表
     * @param batchSize 每批处理的实体数量，默认100
     * @return 更新成功的记录总数
     */
    public Integer batchUpsertInChunks(List<OuterOaEmployeeDataEntity> entities, int batchSize) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 使用默认批次大小
        if (batchSize <= 0) {
            batchSize = 100;
        }

        int totalUpdated = 0;
        int totalSize = entities.size();
        
        // 按批次处理数据
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<OuterOaEmployeeDataEntity> batchList = entities.subList(i, endIndex);
            
            try {
                int batchResult = batchUpdateOutUserId(batchList);
                totalUpdated += batchResult;
                LogUtils.info("Processed batch {}/{}, size={}, updated={}", 
                             (i / batchSize) + 1, (int) Math.ceil(totalSize / (double) batchSize), 
                             batchList.size(), batchResult);
            } catch (Exception e) {
                LogUtils.error("Error processing batch {}/{}: {}", 
                              (i / batchSize) + 1, (int) Math.ceil(totalSize / (double) batchSize), 
                              e.getMessage(), e);
            }
        }
        
        return totalUpdated;
    }

    //需要处理用户映射



    /**
     * 获取并缓存类的字段映射信息
     *
     * @param clazz 需要处理的类
     * @return 字段名到Field对象的映射
     */
    private Map<String, Field> getFieldCache(Class<?> clazz) {
        String cacheKey = clazz.getName();
        Map<String, Field> classFieldCache = new HashMap<>();

        // 处理所有字段，包括带CRMField注解的和普通字段
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            // 缓存字段名映射
            classFieldCache.put(field.getName(), field);

            // 对于有CRMField注解的字段，额外缓存CRM字段码到Field的映射
            SystemAnnotation annotation = field.getAnnotation(SystemAnnotation.class);
            if (annotation != null) {
                classFieldCache.put(annotation.value().getCode(), field);
            }
        }

        return classFieldCache;
    }

    /**
     * 获取标记为USER_ID的字段
     *
     * @param clazz 需要处理的类
     * @return USER_ID字段对象，如果未找到则返回null
     */
    private Field getUserIdField(Class<?> clazz) {
        for (Field field : clazz.getDeclaredFields()) {
            SystemAnnotation annotation = field.getAnnotation(SystemAnnotation.class);
            if (annotation != null && annotation.value() == CRMEmployeeFiledEnum.USER_ID) {
                field.setAccessible(true);
                return field;
            }
        }
        return null;
    }

    /**
     * 获取标记为OWNER_DEPARTMENT的字段
     *
     * @param clazz 需要处理的类
     * @return OWNER_DEPARTMENT字段对象，如果未找到则返回null
     */
    public Field getDepartmentField(Class<?> clazz) {
        for (Field field : clazz.getDeclaredFields()) {
            SystemAnnotation annotation = field.getAnnotation(SystemAnnotation.class);
            if (annotation != null && annotation.value() == CRMEmployeeFiledEnum.MAIN_DEPARTMENT) {
                field.setAccessible(true);
                return field;
            }
        }
        return null;
    }

    /**
     * 获取字段值并转换为字符串
     *
     * @param <T>   对象类型
     * @param obj   需要获取字段值的对象
     * @param field 字段对象
     * @return 字段值的字符串表示，如果为null则返回null
     */
    private <T> String getFieldValueAsString(T obj, Field field) {
        if (field == null || obj == null) {
            return null;
        }

        try {
            Object value = field.get(obj);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            LogUtils.warn("Failed to get field value: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将对象字段映射到实体的text字段
     *
     * @param <T>            对象类型
     * @param entity         目标实体
     * @param sourceObj      源数据对象
     * @param fieldCache     字段缓存映射
     * @param fieldToTextMap 字段到text字段的映射关系
     */
    private <T> void mapFieldsToTextFields(OuterOaEmployeeDataEntity entity, T sourceObj, Map<String, Field> fieldCache,
            Map<String, String> fieldToTextMap) {
        fieldToTextMap.forEach((fieldName, textField) -> {
            Field field = fieldCache.get(fieldName);
            if (field == null) {
                return; // 跳过不存在的字段
            }

            String value = getFieldValueAsString(sourceObj, field);
            if (value == null) {
                return; // 跳过值为null的字段
            }

            // 根据映射设置对应的text字段
            switch (textField) {
            case "text1":
                entity.setText1(value);
                break;
            case "text2":
                entity.setText2(value);
                break;
            case "text3":
                entity.setText3(value);
                break;
            case "text4":
                entity.setText4(value);
                break;
            default:
                LogUtils.warn("Unsupported text field: {}", textField);
            }
        });
    }

    /**
     * 设置部门ID到实体中
     *
     * @param <T>       对象类型
     * @param entity    目标实体
     * @param sourceObj 源数据对象
     * @param deptField 部门字段对象
     */
    private <T> void setDepartmentId(OuterOaEmployeeDataEntity entity, T sourceObj, Field deptField) {
        if (deptField == null || sourceObj == null) {
            return;
        }

        try {
            Object deptValue = deptField.get(sourceObj);
            if (deptValue == null) {
                return;
            }

            // 处理不同类型的部门字段
            if (deptValue instanceof List) {
                // 如果是列表类型，取第一个元素
                List<?> deptList = (List<?>) deptValue;
                if (!deptList.isEmpty()) {
                    Object firstDept = deptList.get(0);
                    entity.setOutDeptId(String.valueOf(firstDept));
                }
            } else {
                // 如果是单个值类型，直接转换
                entity.setOutDeptId(String.valueOf(deptValue));
            }
        } catch (Exception e) {
            LogUtils.warn("Failed to get department field value: {}", e.getMessage());
        }
    }

    private IPage<OuterOaEmployeeDataEntity> queryUnboundEmployeesByField(
            OuterOaEmployeeDataParams outerOaEmployeeDataParams, Integer pageNo, Integer pageSize) {
        try {
            // 创建分页对象
            Page<OuterOaEmployeeDataEntity> page = new Page<>(pageNo, pageSize);
            // 调用Mapper执行查询
            IPage<OuterOaEmployeeDataEntity> result = outerOaEmployeeDataMapper.queryUnboundEmployeesByField(page,
                    outerOaEmployeeDataParams);

            LogUtils.info(
                    "Successfully queried unbounded employee data by params: outEa={}, channel={}, appId={}, page={}, size={}, total={}",
                    outerOaEmployeeDataParams.getOutEa(), outerOaEmployeeDataParams.getChannel(),
                    outerOaEmployeeDataParams.getAppId(), pageNo, pageSize, result.getTotal());
            return result;
        } catch (Exception e) {
            LogUtils.error(
                    "Failed to query unbounded employee data by params: outEa={}, channel={}, appId={}, page={}, error={}",
                    outerOaEmployeeDataParams.getOutEa(), outerOaEmployeeDataParams.getChannel(),
                    outerOaEmployeeDataParams.getAppId(), pageNo, e.getMessage(), e);
            return new Page<>(pageNo, pageSize);
        }
    }

    private OuterOaEmployeeDataParams convertDynamicParams(String dcId, ChannelEnum channelEnum,
            OuterOaEmployeeDataParams outerOaEmployeeDataParams, String outerOAFieldKey, String outerOAFieldValue) {
        // 获取配置
        OuterOaConfigInfoEntity configEntity = outerOaConfigInfoManager
                .getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, dcId);

        // 获取字段映射配置
        String dbField = getDbFieldFromConfig(configEntity.getConfigInfo(), outerOAFieldKey);
        if (dbField == null) {
            LogUtils.info("not query employee field:{}", outerOAFieldKey);
            return null;
        }

        try {
            // 通过反射设置字段值
            Field field = OuterOaEmployeeDataParams.class.getDeclaredField(dbField);
            field.setAccessible(true);
            field.set(outerOaEmployeeDataParams, outerOAFieldValue);
            return outerOaEmployeeDataParams;
        } catch (Exception e) {
            LogUtils.error("反射设置字段失败: field={}, value={}", dbField, outerOAFieldValue, e);
            return null;
        }
    }


    /**
     * 以部门维度删除人员
     */
    public Integer delelteDataByDeptId(String outEa, ChannelEnum channel, String appId, String outerDeptId) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId) || StringUtils.isEmpty(outerDeptId)) {
            LogUtils.warn("Invalid parameters for deleteInvisibleUsers by department: outEa={}, channel={}, appId={}, outerDeptId={}", 
                outEa, channel, appId, outerDeptId);
            return 0;
        }

        try {
            // 构建删除条件
            LambdaQueryWrapper<OuterOaEmployeeDataEntity> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                    .eq(OuterOaEmployeeDataEntity::getChannel, channel)
                    .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                    .eq(OuterOaEmployeeDataEntity::getOutDeptId, outerDeptId);

            // 执行删除操作
            int deletedCount = outerOaEmployeeDataMapper.delete(deleteWrapper);
            
            LogUtils.info("Successfully deleted {} employees in department: outEa={}, channel={}, appId={}, outerDeptId={}", 
                deletedCount, outEa, channel, appId, outerDeptId);
            
            return deletedCount;
        } catch (Exception e) {
            LogUtils.error("Failed to delete employees by department: outEa={}, channel={}, appId={}, outerDeptId={}, error={}", 
                outEa, channel, appId, outerDeptId, e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 根据dingEmpId返回列表的数据
     */
    public <T extends BaseOuterEmployeeObject> T findDingTalkEmployeeObject(ChannelEnum channelEnum, String fsEa, String dingEmpId, String appId) {
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        final OuterOaEmployeeDataEntity dataEntity = queryByChannelAndAppIdAndEmpId(ChannelEnum.dingding, appId, outEa, dingEmpId);

        return Optional.ofNullable(dataEntity)
                .map(OuterOaEmployeeDataEntity::getOutUserInfo)
                .map(json -> (T) json.toJavaObject(channelEnum.getEmployeeObjectClass()))
                .orElse(null);
    }
}