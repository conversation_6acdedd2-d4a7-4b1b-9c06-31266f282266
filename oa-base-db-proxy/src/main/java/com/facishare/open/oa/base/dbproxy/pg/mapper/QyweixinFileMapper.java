package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.facishare.open.oa.base.dbproxy.pg.entity.QyweixinFileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Mapper 接口 - 业微信文件
 */
@Mapper
public interface QyweixinFileMapper extends BaseMapper2<QyweixinFileEntity> {
    @Update("<script>" +
            "INSERT INTO qyweixin_file " +
            "(id, fs_ea, media_id, npath, url, file_size, type, create_at, err_msg, create_time, update_time) " +
            "VALUES " +
            "<foreach collection='list' item='item' separator=','> " +
            "    ( " +
            "    #{item.id}, #{item.fsEa}, #{item.mediaId}, #{item.npath}, #{item.url}, " +
            "    #{item.fileSize}, #{item.type}, #{item.createAt}, #{item.errMsg}, " +
            "    #{item.createTime}, #{item.updateTime} " + // 使用实体类中的时间字段
            "    ) " +
            "</foreach> " +
            "ON CONFLICT (id) " + // 使用主键作为冲突条件 fs_ea, npath, url
            "DO UPDATE SET " +
            "media_id = EXCLUDED.media_id, " +
            "file_size = EXCLUDED.file_size, " +
            "type = EXCLUDED.type, " +
            "create_at = EXCLUDED.create_at, " +
            "err_msg = EXCLUDED.err_msg, " +
            "update_time = EXCLUDED.update_time " + // 使用 EXCLUDED.update_time
            "</script>")
    Integer batchUpsertInfos(@Param("list") List<QyweixinFileEntity> entities);
}
