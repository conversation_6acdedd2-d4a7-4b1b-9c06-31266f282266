package com.facishare.open.ding.provider.model.result.crm;

import com.google.gson.annotations.SerializedName;
import java.util.Map;
import lombok.Data;

import java.util.List;

/**
 * Created by system on 2018/5/15.
 */
@Data
public class SalesOrderProductListData {

    /** 记录总数 **/
    private Integer totalNumber;

    /** 订单关联的产品列表 **/
    private List<Map<String, Object>> data;

    /*private List<SalesOrderProduct> data;

    @Data
    public static class SalesOrderProduct {

        *//** 产品ID **//*
        @SerializedName(value = "product_id")
        private String productId;

        *//** 价格(元) **//*
        @SerializedName(value = "product_price")
        private String productPrice;

        *//** 折扣 **//*
        private String discount;

        *//** 销售单价(元) **//*
        @SerializedName(value = "sales_price")
        private String salesPrice;

        *//** 数量 **//*
        private String quantity;

        *//** 金额小计 **//*
        @SerializedName(value = "subtotal")
        private String subTotal;

    }*/

}
