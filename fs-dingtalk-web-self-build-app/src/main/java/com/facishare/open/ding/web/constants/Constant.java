package com.facishare.open.ding.web.constants;

import com.google.common.collect.Lists;
import org.omg.CORBA.PUBLIC_MEMBER;

import java.util.HashSet;
import java.util.List;

/**
 * @ClassName: Constant
 * @Description: Constant
 * @datetime 2019/3/11 16:13
 * @Version 1.0
 */
public class Constant {
    /**
     * 应用的SuiteKey，登录开发者后台，点击应用管理，进入应用详情可见
     * 企业内部应用开发使用企业corpId
     */
    public static final String SUITE_KEY = "dingc9bdc7e92b060e7135c2f4657eb6378f";

    /**
     * 回调URL加解密用。应用的数据加密密钥，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String ENCODING_AES_KEY = "twki1srsvwm2vaxecwyy2xxllc4r5t0zivt1qropp9s";

    /**
     * 回调URL签名用。应用的签名Token, 登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String TOKEN = "iMY3uFx9DPCc5S17i7Y";
    //不使用回调地址
    public static final Integer IS_NOT_CALLBACK = 1;
    //使用回调地址
    public static final Integer IS_CALLBACK = 0;
    /**
     * 长轮询超时时间
     */
    public static long LONG_POLLING_TIME_OUT = 10000L;

    /**
     * 注册回调事件
     */
    public static List<String> CALL_BACK_TAG = Lists.newArrayList(
            "user_add_org","user_modify_org", "user_leave_org","org_dept_create","org_dept_modify","org_dept_remove");
}
