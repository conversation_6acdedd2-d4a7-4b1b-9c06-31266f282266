package com.facishare.open.ding.provider.crm;

import com.facishare.open.ding.provider.config.ConfigCenter;

/**
 * <AUTHOR> url的组装
 * @Date 2021/2/23 19:46
 * @Version 1.0
 */
public class CrmUrlUtils {

    //crm rest接口前缀
    public static String preUrl= ConfigCenter.CRM_REST_OBJ_URL;


    /**
     * 创建部门
     */
    public static String createDeptUrl(){
        return preUrl.concat("/DepartmentObj/action/Add");
    }

    /**
     * 删除部门 '
     */
    public static String stopDeptUrl(){
        return preUrl.concat("/DepartmentObj/action/BulkStop");
    }
    /**
     * 修改部门
     */
    public static String updateDeptUrl(){
        return preUrl.concat("/DepartmentObj/action/IncrementUpdate");
    }
    /**
     * 创建员工
     */
    public static String createUserUrl(){
        return preUrl.concat("/PersonnelObj/action/Add");
    }
    /**
     * 修改员工
     */
    public static String modifyUserUrl(){
        return preUrl.concat("/PersonnelObj/action/Edit");
    }
    /**
     * 停用员工
     */
    public static String stopUserUrl(){
        return preUrl.concat("/PersonnelObj/action/Edit");
    }

    /**
     *  字段查询对象
     */
    public static String queryList(String apiName){
        return preUrl.concat(apiName).concat("/controller/List");
    }
}

