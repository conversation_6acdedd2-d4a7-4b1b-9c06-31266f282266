package com.facishare.open.ding.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/7/27 15:05
 * @Version 1.0
 */
@Data
public class CreateCrmEmployeeVo implements Serializable {
    //主键ID
    private Integer id;
    private String name;
    private String unionid;

    private String gender = "M";

    private String mobile;

    private String dingEmployeeId;

    private Integer updateBy;

    /**
     * 纷享ei
     **/
    private Integer ei;

    /**
     * 主属钉钉部门
     */
    private Long dingDeptId;

    /**
     * 汇报对象
     */
    private String managerUserid;

    /**
     * crm的主属部门
     */
    private String crmMainDeptId;

    /**
     * 附属部门
     */

    private List<String> crmViceDepts;

    /**
     * 对应纷享的员工ID
     */

    private Integer crmEmpId;
    //汇报对象
    private Integer leader;
    //职位
    private String position;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 工号
     */
    private String employeeNumber;

    /**
     * 性别
     */
    private Integer sexType;

    /**
     * 入职日期
     */
    private Long hiredDate;
}
