package com.facishare.open.ding.api.service;

import com.facishare.open.ding.provider.arg.SendTextCardContextArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextCardMessageResult;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextMessageResult;

public interface ExternalMessageService {
    /**
     * 发送文本消息
     * @param sendTextMessageArg
     * @return
     */
    SendTextMessageResult sendTextMessage(SendTextMessageArg sendTextMessageArg);

    /**
     * 发送文本卡片消息
     * @param sendTextCardContextArg
     * @return
     */
    SendTextCardMessageResult sendTextCardMessage(SendTextCardContextArg sendTextCardContextArg);

    /**
     * 发送文本消息
     * http接口调用，不受待办提醒开关控制
     * @param sendTextMessageArg
     * @return
     */
    SendTextMessageResult sendCommonTextMessage(SendTextMessageArg sendTextMessageArg);

    /**
     * 发送文本卡片消息
     * http接口调用，不受待办提醒开关控制
     * @param sendTextCardMessageArg
     * @return
     */
    SendTextCardMessageResult sendCommonTextCardMessage(SendTextCardMessageArg sendTextCardMessageArg);

}
