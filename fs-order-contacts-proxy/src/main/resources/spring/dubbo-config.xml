<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <description>dubbo配置</description>

    <!-- Dubbo应用名称-->
    <dubbo:application id="openOrderContactsApplication"
                       name="${dubbo.application.name}"
                       organization="fs-online-order-contacts"/>
    <!--协议-->
    <dubbo:protocol id="openOrderContactsProtocol"
                    name="${dubbo.protocol.name}"
                    dispatcher="${dubbo.protocol.dispatcher}"
                    threadpool="${dubbo.protocol.threadpool}"
                    threads="${dubbo.protocol.threads}">
    </dubbo:protocol>
    <!--注册中心, 在本地开发环境请采用直连方式，可把 register、subscribe 都配置为false-->
    <dubbo:registry id="openOrderContactsRegistry"
                    protocol="${dubbo.registry.protocol}"
                    address="${dubbo.registry.address}"
                    check="false"
                    file="${dubbo.registry.file}">
    </dubbo:registry>

    <!--提供者配置-->
    <dubbo:provider id="openOrderContactsProvider"
                    application="openOrderContactsApplication"
                    registry="openOrderContactsRegistry"
                    protocol="openOrderContactsProtocol"
                    port="${dubbo.provider.port}"
                    cluster="${dubbo.provider.cluster}"
                    loadbalance="${dubbo.provider.loadbalance}"
                    retries="${dubbo.provider.retries}"
                    timeout="${dubbo.provider.timeout}"
                    delay="-1"
                    filter="tracerpc">
    </dubbo:provider>

    <!--消费者配置-->
    <dubbo:registry id="openOrderContactsClientRegistry"
                    protocol="${dubbo.registry.protocol}"
                    address="${dubbo.registry.address}"
                    check="false">
    </dubbo:registry>
    <dubbo:consumer id="openOrderContactsConsumer"
                    registry="openOrderContactsClientRegistry"
                    init="false"
                    check="${dubbo.consumer.check}"
                    timeout="${dubbo.consumer.timeout}"
                    retries="${dubbo.consumer.retries}"
                    filter="tracerpc"/>
</beans>