package com.facishare.open.order.contacts.proxy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderMultipleProductArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.arg.QueryCustomerArg;
import com.facishare.open.order.contacts.proxy.api.data.Crm112CustomerObjectData;
import com.facishare.open.order.contacts.proxy.api.data.CrmCustomerObjectData;
import com.facishare.open.order.contacts.proxy.api.data.FsCustomerObjectData;
import com.facishare.open.order.contacts.proxy.api.data.FsObjectCustomer;
import com.facishare.open.order.contacts.proxy.api.enums.CrmProductTypeEnum;
import com.facishare.open.order.contacts.proxy.api.model.CrmOrderMappingModel;
import com.facishare.open.order.contacts.proxy.api.model.CrmOrderProductModel;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.config.ConfigCenter;
import com.facishare.open.order.contacts.proxy.manager.CrmManager;
import com.facishare.webhook.common.dao.paas.dao.CrmCustomer;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import static com.facishare.webhook.common.util.Constant.CRM_CUSTOMER_OBJECT_API_ENTERPRISE_ACCOUNT;

@Slf4j
@Service("fsOrderServiceProxy")
public class FsOrderServiceProxyImpl implements FsOrderServiceProxy {
    @Resource
    private CrmManager crmManager;
    @Resource
    private FsEmployeeAndDepartmentProxy fsEmployeeAndDepartmentProxy;

    @Override
    public Result<Void> createCustomer(CreateCustomerArg arg) {
        //创建客户
        Result<String> customer = crmManager.createCustomer(arg);
        log.info("FsOrderServiceProxyImpl.createCustomer,customer={}", customer);
        if(!customer.isSuccess()) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> createCrmOrder(CreateCrmOrderArg arg) {
        //创建订单
        Result<String> crmOrder = crmManager.createCrmOrder(arg);
        log.info("FsOrderServiceProxyImpl.createCrmOrder,crmOrder={}", crmOrder);
        if(!crmOrder.isSuccess()) {
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<CrmOrderMappingModel>> batchCreateCrmOrder(List<CreateCrmOrderArg> orderArgList) {
        List<CrmOrderMappingModel> mappingModelList = new ArrayList<>();
        for(CreateCrmOrderArg arg : orderArgList) {
            CrmOrderMappingModel mappingModel = new CrmOrderMappingModel();
            mappingModel.setProductId(arg.getCrmOrderProductInfo().getProductId());
            //创建订单
            Result<String> crmOrder = crmManager.createCrmOrder(arg);
            log.info("FsOrderServiceProxyImpl.batchCreateCrmOrder,crmOrder={}", crmOrder);
            if(crmOrder.isSuccess()) {
                mappingModel.setOrderId(crmOrder.getData());
            }
            mappingModelList.add(mappingModel);
        }
        return Result.newSuccess(mappingModelList);
    }

    @Override
    public Result<Void> openEnterprise(CreateCustomerArg customerArg, CreateCrmOrderArg orderArg) {
        //创建客户
        Result<Void> result = createCustomer(customerArg);
        if(result.isSuccess()==false) {
            return result;
        }
        //创建订单
        result = createCrmOrder(orderArg);
        if(result.isSuccess()==false) {
            return result;
        }
        return Result.newSuccess();
    }

//    @Override
//    public Result<Void> onEnterpriseOpened(Integer ei, String ea, String enterpriseName) {
//        Result<Void> result = fsEventService.onEnterpriseOpened(ei,ea,enterpriseName);
//        return result;
//    }

    @Override
    public Result<List<ObjectData>> queryCustomer(Integer ei, String outEa) {
        LogUtils.info("FsOrderServiceProxyImpl.queryCustomer,outEa={}", outEa);
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/AccountObj/controller/List";
        String param = "{\n" +
                "    \"serializeEmpty\":false,\n" +
                "    \"extractExtendInfo\":true,\n" +
                "    \"object_describe_api_name\":\"AccountObj\",\n" +
                "    \"search_template_id\":\"{search_template_id}\",\n" +
                "    \"include_describe\":false,\n" +
                "    \"include_layout\":false,\n" +
                "    \"search_template_type\":\"default\",\n" +
                "    \"ignore_scene_record_type\":false,\n" +
                "    \"search_query_info\":\"{\\\"limit\\\":20,\\\"offset\\\":0,\\\"filters\\\":[{\\\"field_name\\\":\\\"field_fsj82__c\\\",\\\"field_values\\\":[\\\"{outEa}\\\"],\\\"operator\\\":\\\"EQ\\\"}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\n" +
                "    \"pageSizeOption\":[\n" +
                "        20\n" +
                "    ]\n" +
                "}";
        param = param.replace("{search_template_id}", ConfigCenter.CRM_ACCOUNT_TEMPLATE_ID).replace("{outEa}", outEa);

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.queryCustomer,ei={},outEa={},result={}", ei, outEa, result);
        return result;
    }

    @Override
    public Result<String> createObjCustomer(FsObjectCustomer fsObjectCustomer) {
        return crmManager.createObjCustomer(fsObjectCustomer);
    }

    @Override
    public Result<Void> deleteCustomer(String customerId) {
        return crmManager.deleteCustomer(customerId);
    }

    @Override
    public Result<String> createCustomer2(CreateCustomerArg arg) {
        Result<String> customer = crmManager.createCustomer2(arg);
        log.info("FsOrderServiceProxyImpl.createCustomer2,customer={}", customer);
        return customer;
    }

    @Override
    public Result<List<ObjectData>> queryCustomerBySearchTemplate(QueryCustomerArg arg) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        String env = System.getProperty("process.profile");
        try {
            if(StringUtils.isEmpty(env) || env.equals("fstest") || env.equals("fstest-gray")) {
                Crm112CustomerObjectData crm112CustomerObjectData = new Crm112CustomerObjectData();
                BeanUtils.copyProperties(arg, crm112CustomerObjectData);
                searchTemplateQuery.setFilters(generateFiltersForNonNullFields(crm112CustomerObjectData));
            } else {
                CrmCustomerObjectData crmCustomerObjectData = new CrmCustomerObjectData();
                BeanUtils.copyProperties(arg, crmCustomerObjectData);
                searchTemplateQuery.setFilters(generateFiltersForNonNullFields(crmCustomerObjectData));
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        Result<List<ObjectData>> result = crmManager.queryBySearchTemplate("AccountObj", searchTemplateQuery);
        log.info("FsOrderServiceProxyImpl.queryCustomerBySearchTemplate,result={}", result);
        return result;
    }

    public static List<Filter> generateFiltersForNonNullFields(Object obj) throws IllegalAccessException {
        List<Filter> filters = new ArrayList<>();
        Class<?> clazz = obj.getClass();

        // 遍历对象的所有字段
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true); // 确保可以访问私有字段
            Object value = field.get(obj);

            // 如果字段有值，则创建相应的过滤器
            if (value != null) {
                JSONField jsonField = field.getAnnotation(JSONField.class);
                String fieldName = jsonField != null ? jsonField.name() : field.getName();

                Filter filter = new Filter();
                filter.setFieldName(fieldName);
                filter.setFieldValues(Lists.newArrayList(value.toString())); // 假设字段值可以转换为字符串
                filter.setOperator("EQ"); // 目前只支持相等操作

                filters.add(filter);
            }
        }
        return filters;
    }

    @Override
    public Result<Void> createCrmOrder2(CreateCrmOrderArg arg) {
        //通过fsEa查询客户
        QueryCustomerArg queryCustomerArg = new QueryCustomerArg();
        queryCustomerArg.setEnterpriseAccount(arg.getCrmOrderDetailInfo().getEnterpriseAccount());
        // 通过ea查询客户
        Result<List<ObjectData>> queryCustomerBySearchTemplateResult = queryCustomerBySearchTemplate(queryCustomerArg);
        if (!queryCustomerBySearchTemplateResult.isSuccess() || CollectionUtils.isEmpty(queryCustomerBySearchTemplateResult.getData())) {
            log.info("FsOrderServiceProxyImpl.createCrmOrder2,queryCustomerBySearchTemplateResult={}", queryCustomerBySearchTemplateResult);
            //等待重试
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            queryCustomerBySearchTemplateResult = queryCustomerBySearchTemplate(queryCustomerArg);
            if (!queryCustomerBySearchTemplateResult.isSuccess() || CollectionUtils.isEmpty(queryCustomerBySearchTemplateResult.getData())) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }
        }

        //客户id
        ObjectData customerData = queryCustomerBySearchTemplateResult.getData().get(0);

        //查询订单产品
        String productId = arg.getCrmOrderProductInfo().getProductId();
        HashMap<String, CrmOrderProductModel> crmProductIdMap = JSON.parseObject(ConfigCenter.crm_order_product_id, new TypeToken<HashMap<String, CrmOrderProductModel>>() {
        }.getType());
        CrmOrderProductModel crmOrderProductModel = null;
        if (crmProductIdMap.containsKey(productId)) {
            crmOrderProductModel = crmProductIdMap.get(productId);
        } else {
            //通过productId查询产品
            Result<List<ObjectData>> queryProductResult = queryProductById(productId);
            log.info("FsOrderServiceProxyImpl.createCrmOrder2,queryProductResult={}", queryProductResult);
            if (!queryProductResult.isSuccess() || CollectionUtils.isEmpty(queryProductResult.getData())) {
                return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
            }
            crmOrderProductModel = new CrmOrderProductModel();
            if (ObjectUtils.isNotEmpty(queryProductResult.getData().get(0).get("name"))) {
                crmOrderProductModel.setProductName(queryProductResult.getData().get(0).get("name").toString());
            }
            crmOrderProductModel.setPriceBookProductId(productId + "1");

//            //根据映射
//            String env = System.getProperty("process.profile");
//            String category;
//            if(StringUtils.isEmpty(env) || env.equals("fstest") || env.equals("fstest-gray")) {
//                category = queryProductResult.getData().get(0).get("field_91322__c").toString();
//            } else {
//                category = queryProductResult.getData().get(0).get("field_2wOsi__c").toString();
//            }
//            log.info("FsOrderServiceProxyImpl.createCrmOrder2,category={}", category);
//            CrmProductTypeEnum productType = CrmProductTypeEnum.from(category);
//            crmOrderProductModel.setRecordType(productType.getRecordType());

            //新分版2024
            crmOrderProductModel.setRecordType(ConfigCenter.new_record_type_2024);
        }

        Result<Void> result = crmManager.createCrmOrder2(arg, crmOrderProductModel, customerData);
        log.info("FsOrderServiceProxyImpl.createCrmOrder2,result={}", result);
        return result;
    }

    @Override
    public Result<List<ObjectData>> queryProductById(String productId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        Filter filter = new Filter();
        filter.setFieldName("_id");
        filter.setFieldValues(Lists.newArrayList(productId));
        filter.setOperator("EQ");
        List<Filter> filters = new ArrayList<>();
        filters.add(filter);
        searchTemplateQuery.setFilters(filters);
        Result<List<ObjectData>> result = crmManager.queryBySearchTemplate("ProductObj", searchTemplateQuery);
        log.info("FsOrderServiceProxyImpl.queryProductById,result={}", result);
        return result;
    }

    @Override
    public Result<Void> createCrmOrderMultipleProduct(CreateCrmOrderMultipleProductArg arg) {
        //通过fsEa查询客户
        QueryCustomerArg queryCustomerArg = new QueryCustomerArg();
        queryCustomerArg.setEnterpriseAccount(arg.getCrmOrderDetailInfo().getEnterpriseAccount());
        // 通过ea查询客户
        Result<List<ObjectData>> queryCustomerBySearchTemplateResult = queryCustomerBySearchTemplate(queryCustomerArg);
        if (!queryCustomerBySearchTemplateResult.isSuccess() || CollectionUtils.isEmpty(queryCustomerBySearchTemplateResult.getData())) {
            log.info("FsOrderServiceProxyImpl.createCrmOrder2,queryCustomerBySearchTemplateResult={}", queryCustomerBySearchTemplateResult);
            //等待重试
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            queryCustomerBySearchTemplateResult = queryCustomerBySearchTemplate(queryCustomerArg);
            if (!queryCustomerBySearchTemplateResult.isSuccess() || CollectionUtils.isEmpty(queryCustomerBySearchTemplateResult.getData())) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }
        }

        //客户id
        ObjectData customerData = queryCustomerBySearchTemplateResult.getData().get(0);

        //查询订单产品
        //在这里查询吧，如果有其中一个产品失败的话，就不处理了
        List<CrmOrderProductModel> crmOrderProductModels = new ArrayList<>();
        for (CreateCrmOrderMultipleProductArg.CrmOrderProductInfo crmOrderProductInfo : arg.getCrmOrderProductInfo()) {
            //多订单产品
            String productId = crmOrderProductInfo.getProductId();
            HashMap<String, CrmOrderProductModel> crmProductIdMap = JSON.parseObject(ConfigCenter.crm_order_product_id, new TypeToken<HashMap<String, CrmOrderProductModel>>() {
            }.getType());
            CrmOrderProductModel crmOrderProductModel = null;
            if (crmProductIdMap.containsKey(productId)) {
                crmOrderProductModel = crmProductIdMap.get(productId);
            } else {
                //通过productId查询产品
                Result<List<ObjectData>> queryProductResult = queryProductById(productId);
                log.info("FsOrderServiceProxyImpl.createCrmOrder2,queryProductResult={}", queryProductResult);
                if (!queryProductResult.isSuccess() || CollectionUtils.isEmpty(queryProductResult.getData())) {
                    return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
                }
                crmOrderProductModel = new CrmOrderProductModel();
                if (ObjectUtils.isNotEmpty(queryProductResult.getData().get(0).get("name"))) {
                    crmOrderProductModel.setProductName(queryProductResult.getData().get(0).get("name").toString());
                }
                crmOrderProductModel.setPriceBookProductId(productId + "1");
                //新分版2024
                crmOrderProductModel.setRecordType(ConfigCenter.new_record_type_2024);
            }
            crmOrderProductModel.setProductId(productId);
            crmOrderProductModels.add(crmOrderProductModel);
        }

        Result<Void> result = crmManager.createCrmOrderMultipleProduct(arg, crmOrderProductModels, customerData);
        log.info("FsOrderServiceProxyImpl.createCrmOrder2,result={}", result);
        return result;

    }
}
