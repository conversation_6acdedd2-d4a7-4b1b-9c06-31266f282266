package com.facishare.open.order.contacts.proxy.api.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LogUtils {
    private static Logger logger = LoggerFactory.getLogger(LogUtils.class);

    public static void trace(String msg) {
        logger.trace(msg);
    }

    public static void trace(String msg,Object obj) {
        logger.trace(msg,obj);
    }

    public static void trace(String msg,Object obj1,Object obj2) {
        logger.trace(msg,obj1,obj2);
    }

    public static void trace(String msg,Object...objs) {
        logger.trace(msg,objs);
    }

    public static void trace(String msg,Throwable e) {
        logger.trace(msg,e);
    }

    public static void debug(String msg) {
        logger.debug(msg);
    }

    public static void debug(String msg,Object obj) {
        logger.debug(msg,obj);
    }

    public static void debug(String msg,Object obj1,Object obj2) {
        logger.debug(msg,obj1,obj2);
    }

    public static void debug(String msg,Object...objs) {
        logger.debug(msg,objs);
    }

    public static void debug(String msg,Throwable e) {
        logger.debug(msg,e);
    }

    public static void info(String msg) {
        logger.info(msg);
    }

    public static void info(String msg,Object obj) {
        logger.info(msg,obj);
    }

    public static void info(String msg,Object obj1,Object obj2) {
        logger.info(msg,obj1,obj2);
    }

    public static void info(String msg,Object...objs) {
        logger.info(msg,objs);
    }

    public static void info(String msg,Throwable e) {
        logger.info(msg,e);
    }

    public static void warn(String msg) {
        logger.warn(msg);
    }

    public static void warn(String msg,Object obj) {
        logger.warn(msg,obj);
    }

    public static void warn(String msg,Object obj1,Object obj2) {
        logger.warn(msg,obj1,obj2);
    }

    public static void warn(String msg,Object...objs) {
        logger.warn(msg,objs);
    }

    public static void warn(String msg,Throwable e) {
        logger.warn(msg,e);
    }

    public static void error(String msg) {
        logger.error(msg);
    }

    public static void error(String msg,Object obj) {
        logger.error(msg,obj);
    }

    public static void error(String msg,Object obj1,Object obj2) {
        logger.error(msg,obj1,obj2);
    }

    public static void error(String msg,Object...objs) {
        logger.error(msg,objs);
    }

    public static void error(String msg,Throwable e) {
        logger.error(msg,e);
    }
}
