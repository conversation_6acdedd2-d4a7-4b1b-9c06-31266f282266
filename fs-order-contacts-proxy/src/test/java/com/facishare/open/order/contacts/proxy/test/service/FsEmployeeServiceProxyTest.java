package com.facishare.open.order.contacts.proxy.test.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.test.BaseTest;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.data.EmpRoleData;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.manager.CrmManager;
import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.FindEmployeeDtoByFullNameResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class FsEmployeeServiceProxyTest extends BaseTest {
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private CrmManager crmManager;

    @Test
    public void create() {
        FsEmpArg arg = FsEmpArg.builder()
                .ei("85229")
                .name("吴贝-- +++———90))()（）（） *@#$%贝")
                .fullName("吴贝--- ____  ()(()(*@#$%)（）（）贝")
                .isActive(true)
                .leader(Lists.newArrayList("1000"))
                .status("0")
                .mainDepartment(Lists.newArrayList("1000"))
                .build();

        Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode(),FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
        System.out.println(result);
    }

    @Test
    public void batchCreate() {
        for(int i=401;i<10000;i++) {
            FsEmpArg arg = FsEmpArg.builder()
                    .ei("85879")
                    .name("测试员工"+i)
                    .fullName("测试员工"+i)
                    .isActive(true)
                    .leader(Lists.newArrayList("1000"))
                    .status("0")
                    .mainDepartment(Lists.newArrayList("1003"))
                    .build();

            Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode(),FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
            System.out.println(result);
        }
    }

    @Test
    public void update() {
        FsEmpArg arg = FsEmpArg.builder()
                .ei("85229")
                .id("1116")
                .name("吴贝-- +++---90))()（）（） -----贝__85")
                .mainDepartment(Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+""))
                .build();
        Result<Void> result = fsEmployeeServiceProxy.update(arg,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        System.out.println(result);
    }

    @Test
    public void toggle() {
        Integer ei = eieaConverter.enterpriseAccountToId("bbfscs9470");
        Result<Void> result = fsEmployeeServiceProxy.toggle(ei+"","1000",true,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode(),FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode()),
                FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
        System.out.println(result);
    }

    @Test
    public void bulkStop() {
        Integer ei = eieaConverter.enterpriseAccountToId("fsfxcs8780");
        Result<Void> result = fsEmployeeServiceProxy.bulkStop(ei+"",Lists.newArrayList("1000","1003"));
        System.out.println(result);
    }

    @Test
    public void bulkResume() {
        Integer ei = eieaConverter.enterpriseAccountToId("bbfscs9470");
        Result<Void> result = fsEmployeeServiceProxy.bulkResume(ei+"",
                Lists.newArrayList("1002"),
                Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        System.out.println(result);
    }

    @Test
    public void bulkResetMainDepartment() {
        Result<Void> result = fsEmployeeServiceProxy.bulkResetMainDepartment("74860",
                Lists.newArrayList("4173","4174"),
                "1025");
        System.out.println(result);
    }

    @Test
    public void listAll() {
        Result<List<ObjectData>> result = fsEmployeeServiceProxy.listAll(88142,"1005");
        System.out.println(result);
    }

    @Test
    public void listByDepId() {
        Result<List<ObjectData>> result = fsEmployeeServiceProxy.listByDepId(82777,"1003");
        System.out.println(result);
    }

    @Test
    public void addManagerRole() {
        Integer ei = eieaConverter.enterpriseAccountToId("bbfscs9470");
        Result<Void> result = crmManager.addManagerRole(ei, Lists.newArrayList(1000));
        System.out.println(result);
    }

    @Test
    public void batchGetEmployeeDto() {
        Integer ei = eieaConverter.enterpriseAccountToId("fsfxcs8780");
        Result<List<EmployeeDto>> result = fsEmployeeServiceProxy.batchGetEmployeeDto(ei,
                Lists.newArrayList(1002));
        System.out.println(result);
    }

    @Test
    public void batchGetAllEmployeeDto() {
        Integer ei = eieaConverter.enterpriseAccountToId("aly6122655");
        Result<List<EmployeeDto>> result = fsEmployeeServiceProxy.batchGetAllEmployeeDto(ei,
                Lists.newArrayList());
        System.out.println(result);
    }

    @Test
    public void getEmployeesByFullName() {


        Result<FindEmployeeDtoByFullNameResult> result = fsEmployeeServiceProxy.getEmployeesByFullName(74860, "test1");
        System.out.println(result);
    }

    @Test
    public void getEmployeesByName() {
        Result<GetEmployeesDtoByNameResult> result = fsEmployeeServiceProxy.getEmployeesByName(74860, "test1");
        System.out.println(result);
    }

    @Test
    public void getUsersRoles() {
        Result<List<UserRoleVo>> result = fsEmployeeServiceProxy.batchGetUserRoles(74860, Lists.newArrayList("1000"));
        System.out.println(result);
    }
    @Test
    public void batchGetEmployeeRoleCodes() {
        Result<BatchGetRoleCodesByEmployeeIds.Result> result = fsEmployeeServiceProxy.batchGetEmployeeRoleCodes(74860, Lists.newArrayList(1000));
        System.out.println(result);
    }


    @Test
    public void list() {
        Result<List<ObjectData>> result = fsEmployeeServiceProxy.list("84801", "1001");
        System.out.println(result);
    }

    @Test
    public void detail() {
        Result<ObjectData> result = fsEmployeeServiceProxy.detail("84801", "1000");
        System.out.println(result);
    }

    @Test
    public void getRoleListByUserId() {
        Result<List<EmpRoleData>> result = fsEmployeeServiceProxy.getRoleListByUserId("84801", "1000");
        System.out.println(result);
    }

    @Test
    public void addManagerRole1() {
        Result<Void> result = fsEmployeeServiceProxy.addManagerRole(85471, Lists.newArrayList(1003));
        System.out.println(result);
    }

    @Test
    public void getAllEmployees() {
        Result<List<EmployeeDto>> result = fsEmployeeServiceProxy.getAllEmployees(85471);
        System.out.println(result);
    }
}
